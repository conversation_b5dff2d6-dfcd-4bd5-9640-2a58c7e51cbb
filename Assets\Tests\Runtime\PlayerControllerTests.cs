using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;

namespace SquadMateAI.Tests
{
    public class PlayerControllerTests
    {
        private GameObject playerObject;
        private PlayerController playerController;
        private ReviveSystem reviveSystem;

        [SetUp]
        public void SetUp()
        {
            // Create a test player object
            playerObject = new GameObject("TestPlayer");
            playerController = playerObject.AddComponent<PlayerController>();
            
            // Add required components
            playerObject.AddComponent<CharacterController>();
            
            // ReviveSystem will be automatically added by PlayerController.Start()
            playerController.Start();
            reviveSystem = playerController.GetReviveSystem();
        }

        [TearDown]
        public void TearDown()
        {
            if (playerObject != null)
            {
                Object.DestroyImmediate(playerObject);
            }
        }

        [Test]
        public void PlayerController_HasIsDownedProperty()
        {
            // Test that the isDowned property exists and is accessible
            Assert.IsNotNull(playerController);
            
            // Initially player should not be downed
            Assert.IsFalse(playerController.isDowned, "Player should not be downed initially");
        }

        [Test]
        public void PlayerController_IsDownedProperty_ReflectsReviveSystemState()
        {
            // Test that isDowned property correctly reflects ReviveSystem state
            Assert.IsFalse(playerController.isDowned, "Player should not be downed initially");
            Assert.IsFalse(reviveSystem.isDown, "ReviveSystem should not be down initially");
            
            // Down the player
            playerController.GoDown();
            
            // Both should now be true
            Assert.IsTrue(playerController.isDowned, "Player isDowned should be true after going down");
            Assert.IsTrue(reviveSystem.isDown, "ReviveSystem isDown should be true after going down");
        }

        [Test]
        public void PlayerController_IsDownedProperty_UpdatesOnRevive()
        {
            // Down the player first
            playerController.GoDown();
            Assert.IsTrue(playerController.isDowned, "Player should be downed");
            
            // Revive the player
            playerController.Revive();
            
            // Both should now be false
            Assert.IsFalse(playerController.isDowned, "Player isDowned should be false after revive");
            Assert.IsFalse(reviveSystem.isDown, "ReviveSystem isDown should be false after revive");
        }

        [Test]
        public void PlayerController_IsDownedProperty_UpdatesOnDamage()
        {
            // Player starts with full health
            Assert.IsFalse(playerController.isDowned, "Player should not be downed initially");
            
            // Deal enough damage to down the player
            playerController.TakeDamage(playerController.maxHealth);
            
            // Player should now be downed
            Assert.IsTrue(playerController.isDowned, "Player should be downed after taking fatal damage");
            Assert.AreEqual(0f, playerController.currentHealth, "Player health should be 0");
        }

        [Test]
        public void PlayerController_IsDownedMethod_MatchesProperty()
        {
            // Test that IsPlayerDowned() method matches isDowned property
            Assert.AreEqual(playerController.IsPlayerDowned(), playerController.isDowned, 
                "IsPlayerDowned() method should match isDowned property initially");
            
            // Down the player
            playerController.GoDown();
            
            Assert.AreEqual(playerController.IsPlayerDowned(), playerController.isDowned, 
                "IsPlayerDowned() method should match isDowned property after going down");
            
            // Revive the player
            playerController.Revive();
            
            Assert.AreEqual(playerController.IsPlayerDowned(), playerController.isDowned, 
                "IsPlayerDowned() method should match isDowned property after revive");
        }

        [Test]
        public void PlayerController_HealthSystem_Integration()
        {
            // Test integration with health system
            Assert.AreEqual(playerController.maxHealth, playerController.currentHealth, 
                "Player should start with full health");
            
            float damageAmount = 30f;
            float expectedHealth = playerController.maxHealth - damageAmount;
            
            playerController.TakeDamage(damageAmount);
            
            Assert.AreEqual(expectedHealth, playerController.currentHealth, 
                "Player health should decrease by damage amount");
            Assert.IsFalse(playerController.isDowned, "Player should not be downed from partial damage");
        }

        [Test]
        public void PlayerController_EventSystem_OnPlayerDownedChanged()
        {
            // Test that the OnPlayerDownedChanged event is fired correctly
            bool eventFired = false;
            bool eventValue = false;
            
            playerController.OnPlayerDownedChanged += (downed) => {
                eventFired = true;
                eventValue = downed;
            };
            
            // Down the player
            playerController.GoDown();
            
            Assert.IsTrue(eventFired, "OnPlayerDownedChanged event should have fired");
            Assert.IsTrue(eventValue, "Event should indicate player is downed");
            
            // Reset for revive test
            eventFired = false;
            eventValue = true;
            
            // Revive the player
            playerController.Revive();
            
            Assert.IsTrue(eventFired, "OnPlayerDownedChanged event should have fired on revive");
            Assert.IsFalse(eventValue, "Event should indicate player is not downed");
        }
    }
}
