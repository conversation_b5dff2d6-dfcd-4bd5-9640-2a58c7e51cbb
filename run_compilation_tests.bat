@echo off
echo ========================================
echo SquadMateAI Compilation Fix Tests
echo ========================================
echo.

REM Set Unity path (adjust if needed)
set UNITY_PATH="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Unity.exe"

REM Set project path
set PROJECT_PATH=%~dp0

echo Unity Path: %UNITY_PATH%
echo Project Path: %PROJECT_PATH%
echo.

echo Running compilation fix tests...
echo.

REM Run Unity tests in batch mode
%UNITY_PATH% -batchmode -quit -projectPath "%PROJECT_PATH%" -runTests -testPlatform playmode -testResults "%PROJECT_PATH%\TestResults.xml" -logFile "%PROJECT_PATH%\TestLog.txt"

echo.
echo Test execution completed!
echo.

REM Check if test results file exists
if exist "%PROJECT_PATH%\TestResults.xml" (
    echo ✅ Test results saved to: TestResults.xml
) else (
    echo ❌ Test results file not found
)

REM Check if log file exists
if exist "%PROJECT_PATH%\TestLog.txt" (
    echo ✅ Test log saved to: TestLog.txt
    echo.
    echo === RECENT LOG ENTRIES ===
    powershell "Get-Content '%PROJECT_PATH%\TestLog.txt' | Select-Object -Last 20"
) else (
    echo ❌ Test log file not found
)

echo.
echo ========================================
echo Test run complete!
echo ========================================
pause
