using UnityEngine;

/// <summary>
/// Simple script to verify all compilation fixes work correctly
/// Can be attached to any GameObject or run from the context menu
/// </summary>
public class SimpleCompilationTest : MonoBehaviour
{
    [ContextMenu("Test All Compilation Fixes")]
    public void TestAllCompilationFixes()
    {
        Debug.Log("🧪 === COMPILATION FIXES TEST STARTED ===");
        
        bool allTestsPassed = true;
        
        // Test 1: PlayerController.isDowned property
        allTestsPassed &= TestPlayerControllerIsDownedProperty();
        
        // Test 2: SquadMateAgent.hasWeapon property
        allTestsPassed &= TestSquadMateAgentHasWeaponProperty();
        
        // Test 3: PUBGTrainingScenarios.SwitchToNextScenario() method
        allTestsPassed &= TestPUBGScenariosMethod();
        
        // Test 4: Debug.Log capitalization
        allTestsPassed &= TestDebugLogCapitalization();
        
        // Final result
        if (allTestsPassed)
        {
            Debug.Log("🎉 === ALL COMPILATION FIXES WORKING! === 🎉");
        }
        else
        {
            Debug.LogError("❌ === SOME COMPILATION FIXES FAILED === ❌");
        }
        
        Debug.Log("🧪 === COMPILATION FIXES TEST COMPLETED ===");
    }
    
    private bool TestPlayerControllerIsDownedProperty()
    {
        try
        {
            Debug.Log("Testing PlayerController.isDowned property...");
            
            // Create test player
            GameObject testPlayer = new GameObject("TestPlayer");
            PlayerController playerController = testPlayer.AddComponent<PlayerController>();
            testPlayer.AddComponent<CharacterController>();

            // Initialize player (manually add ReviveSystem since Start() is protected)
            testPlayer.AddComponent<ReviveSystem>();
            
            // Test property access
            bool initialState = playerController.isDowned;
            Debug.Log($"  Initial isDowned state: {initialState}");
            
            // Test going down
            playerController.GoDown();
            bool downedState = playerController.isDowned;
            Debug.Log($"  After GoDown(): {downedState}");
            
            // Test revive
            playerController.Revive();
            bool revivedState = playerController.isDowned;
            Debug.Log($"  After Revive(): {revivedState}");
            
            // Clean up
            DestroyImmediate(testPlayer);
            
            bool testPassed = !initialState && downedState && !revivedState;
            Debug.Log($"✅ PlayerController.isDowned test: {(testPassed ? "PASSED" : "FAILED")}");
            
            return testPassed;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ PlayerController.isDowned test FAILED: {e.Message}");
            return false;
        }
    }
    
    private bool TestSquadMateAgentHasWeaponProperty()
    {
        try
        {
            Debug.Log("Testing SquadMateAgent.hasWeapon property...");
            
            // Create test agent
            GameObject testAgent = new GameObject("TestAgent");
            SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
            testAgent.AddComponent<Rigidbody>();
            testAgent.AddComponent<CapsuleCollider>();
            
            // Test property access
            bool initialState = agent.hasWeapon;
            Debug.Log($"  Initial hasWeapon state: {initialState}");
            
            // Test setting weapon
            agent.hasWeapon = true;
            bool weaponState = agent.hasWeapon;
            Debug.Log($"  After setting true: {weaponState}");
            
            // Test removing weapon
            agent.hasWeapon = false;
            bool noWeaponState = agent.hasWeapon;
            Debug.Log($"  After setting false: {noWeaponState}");
            
            // Clean up
            DestroyImmediate(testAgent);
            
            bool testPassed = !initialState && weaponState && !noWeaponState;
            Debug.Log($"✅ SquadMateAgent.hasWeapon test: {(testPassed ? "PASSED" : "FAILED")}");
            
            return testPassed;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ SquadMateAgent.hasWeapon test FAILED: {e.Message}");
            return false;
        }
    }
    
    private bool TestPUBGScenariosMethod()
    {
        try
        {
            Debug.Log("Testing PUBGTrainingScenarios.SwitchToNextScenario() method...");
            
            // Create test scenarios
            GameObject testScenarios = new GameObject("TestScenarios");
            PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
            
            // Test method call
            PUBGTrainingScenarios.PUBGScenarioType initialScenario = scenarios.currentScenario;
            Debug.Log($"  Initial scenario: {initialScenario}");
            
            // Test calling the method
            scenarios.SwitchToNextScenario();
            PUBGTrainingScenarios.PUBGScenarioType newScenario = scenarios.currentScenario;
            Debug.Log($"  After switch: {newScenario}");
            
            // Test multiple calls
            scenarios.SwitchToNextScenario();
            scenarios.SwitchToNextScenario();
            Debug.Log($"  After multiple switches: {scenarios.currentScenario}");
            
            // Clean up
            DestroyImmediate(testScenarios);
            
            bool testPassed = true; // If we got here without exceptions, the method is callable
            Debug.Log($"✅ PUBGTrainingScenarios.SwitchToNextScenario() test: {(testPassed ? "PASSED" : "FAILED")}");
            
            return testPassed;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ PUBGTrainingScenarios.SwitchToNextScenario() test FAILED: {e.Message}");
            return false;
        }
    }
    
    private bool TestDebugLogCapitalization()
    {
        try
        {
            Debug.Log("Testing Debug.Log capitalization...");
            
            // Test that Debug.Log works (not Debug.log)
            Debug.Log("  This message confirms Debug.Log works correctly");
            
            bool testPassed = true; // If we got here, Debug.Log works
            Debug.Log($"✅ Debug.Log capitalization test: {(testPassed ? "PASSED" : "FAILED")}");
            
            return testPassed;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Debug.Log capitalization test FAILED: {e.Message}");
            return false;
        }
    }
    
    // Individual test methods for context menu
    [ContextMenu("Test PlayerController Only")]
    private void TestPlayerControllerOnly()
    {
        TestPlayerControllerIsDownedProperty();
    }
    
    [ContextMenu("Test SquadMateAgent Only")]
    private void TestSquadMateAgentOnly()
    {
        TestSquadMateAgentHasWeaponProperty();
    }
    
    [ContextMenu("Test PUBGScenarios Only")]
    private void TestPUBGScenariosOnly()
    {
        TestPUBGScenariosMethod();
    }
    
    [ContextMenu("Test Debug.Log Only")]
    private void TestDebugLogOnly()
    {
        TestDebugLogCapitalization();
    }
    
    // Auto-run on start if enabled
    [Header("Auto-run Settings")]
    public bool runTestsOnStart = false;
    
    private void Start()
    {
        if (runTestsOnStart)
        {
            TestAllCompilationFixes();
        }
    }
}
