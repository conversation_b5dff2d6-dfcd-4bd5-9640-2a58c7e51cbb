{"name": "com.unity.mobile.android-logcat", "displayName": "Android Logcat", "version": "1.4.5", "unity": "2021.3", "description": "Android Logcat package provides support for:\n - Android log messages\n - Android application memory statistics\n - Input injection\n - Android Screen Capture\n - Android Screen Recorder\n - Stacktrace Utility\n\nClick the 'View documentation' link above for more information.\n\nThe window can be accessed in Unity Editor via 'Window > Analysis > Android Logcat', or simply by pressing 'Alt+6' on Windows or 'Option+6' on macOS. \n\nMake sure to have Android module loaded and switch to Android build target in 'Build Settings' window if the menu doesn't exist.", "keywords": ["Mobile", "Android", "Logcat"], "dependencies": {}, "_upm": {"changelog": "### Fixes & Improvements\n - Fix \"llvm-nm.exe: error: : unknown argument '-e'\" when resolving stacktraces on Windows. Was happening with Unity version 6000.0.44f1"}, "upmCi": {"footprint": "513c048d97f3641e5100a08e7cba5521866156a1"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.mobile.android-logcat@1.4/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.mobile.logcat.git", "type": "git", "revision": "3699423c243d638fcec959c0a8fe07aee8a6c46c"}, "_fingerprint": "0ddcd2133dc3642d39aae1db273b1a54bed03644"}