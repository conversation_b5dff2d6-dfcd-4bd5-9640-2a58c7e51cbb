{"name": "com.unity.polybrush", "displayName": "Polybrush", "version": "1.1.8", "unity": "2018.4", "description": "Mesh painting, sculpting, and geo-scattering tool for Unity.", "keywords": ["modeling", "sculpting", "push", "clay", "terrain"], "samples": [{"displayName": "Shader Examples (Standard)", "description": "Polybrush shader examples compatible with Unity Standard rendering pipeline.", "path": "Samples~/SamplesStandard"}, {"displayName": "Shader Examples (URP)", "description": "Polybrush shader examples compatible with Unity Universal rendering pipeline.", "path": "Samples~/SamplesUniversalRP"}, {"displayName": "Shader Examples (HDRP)", "description": "Polybrush shader examples compatible with Unity High Definition rendering pipeline.", "path": "Samples~/SamplesHDRP"}], "dependencies": {"com.unity.settings-manager": "2.0.1"}, "relatedPackages": {"com.unity.polybrush.tests": "1.1.8"}, "_upm": {"changelog": "### Bug Fixes\n\n- [POLBR-22] Fixed compilation errors related to the use of FindObjectsByType in affected editors."}, "upmCi": {"footprint": "0ef3841b8009d73b7b5936f71d155cceed11d339"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.polybrush@1.1/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/polybrush.git", "type": "git", "revision": "571b9c3665597c648dbe7c7dc7688804260a8ec0"}, "_fingerprint": "9b4531013fbd4797d91318680619ee8780c62b91"}