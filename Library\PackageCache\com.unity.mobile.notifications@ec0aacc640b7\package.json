{"name": "com.unity.mobile.notifications", "displayName": "Mobile Notifications", "version": "2.4.1", "unity": "2021.3", "description": "Mobile Notifications package adds support for scheduling local repeatable or one-time notifications on iOS and Android.\n\nOn iOS receiving of push notifications is also supported.", "keywords": ["Android", "iOS", "Notifications", "Mobile"], "dependencies": {"com.unity.modules.androidjni": "1.0.0"}, "_upm": {"changelog": "### Fixes:\n- [Android] Add missing proguard rule for NotificationChannelWrapper."}, "upmCi": {"footprint": "a751e015372bd434f2fc70e19468784fee864763"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.mobile.notifications@2.4/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.mobile.notifications.git", "type": "git", "revision": "6cde88c42d5e47f1810284aded41399c52273425"}, "_fingerprint": "ec0aacc640b7d2e724393686de37e1602e8e2343"}