{"context": {"projectPath": "C:/squadmateai/Packages", "unityVersion": "6000.1.6f1"}, "inputs": ["C:\\squadmateai\\Packages\\manifest.json", "C:\\squadmateai\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.ai.navigation@2.0.8": {"name": "com.unity.ai.navigation", "displayName": "AI Navigation", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ai.navigation@eb5635ad590d", "fingerprint": "eb5635ad590d47cef2a5c920d9475cc222db3f67", "editorCompatibility": "6000.0.0a1", "version": "2.0.8", "source": "registry", "testable": false}, "com.unity.collab-proxy@2.5.1": {"name": "com.unity.collab-proxy", "displayName": "Version Control", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.collab-proxy@c810163b1645", "fingerprint": "c810163b1645af64e676552b0e8d0b3c575991e6", "editorCompatibility": "2020.3.48f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.feature.characters-animation@1.0.0": {"name": "com.unity.feature.characters-animation", "displayName": "3D Characters and Animation", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.feature.characters-animation@62cc2ae02a69", "fingerprint": "62cc2ae02a691f0bf7ba8b57ffbef685bcfa0902", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.feature.development@1.0.2": {"name": "com.unity.feature.development", "displayName": "Engineering", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.feature.development@767aadbc6eb7", "fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef", "version": "1.0.2", "source": "builtin", "testable": false}, "com.unity.feature.mobile@1.0.0": {"name": "com.unity.feature.mobile", "displayName": "Mobile", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.feature.mobile@00f8a2b6a6fb", "fingerprint": "00f8a2b6a6fb9c01b3e6d2ada669d3dc43945443", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.feature.worldbuilding@1.0.1": {"name": "com.unity.feature.worldbuilding", "displayName": "3D World Building", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.feature.worldbuilding@d247f3a881a3", "fingerprint": "d247f3a881a38e10db11c29399be05dc6f27b501", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.ide.vscode@1.2.5": {"name": "com.unity.ide.vscode", "displayName": "Visual Studio Code Editor", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ide.vscode@82e68656b7bd", "fingerprint": "82e68656b7bdc5da8beb1fbaf1bb4ecf13456d51", "editorCompatibility": "2019.2.0a12", "version": "1.2.5", "source": "registry", "testable": false}, "com.unity.ml-agents@3.0.0": {"name": "com.unity.ml-agents", "displayName": "ML Agents", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ml-agents@2b4ded88494d", "fingerprint": "2b4ded88494d1338cea1b3eb7364ff4dfe142399", "editorCompatibility": "2023.2.0a1", "version": "3.0.0", "source": "registry", "testable": false}, "com.unity.multiplayer.playmode@1.4.3": {"name": "com.unity.multiplayer.playmode", "displayName": "Multiplayer Play Mode", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.multiplayer.playmode@50e3dba5f9d6", "fingerprint": "50e3dba5f9d669a30d286824f8656452f51a6790", "editorCompatibility": "6000.1.0b1", "version": "1.4.3", "source": "registry", "testable": false}, "com.unity.render-pipelines.universal@17.1.0": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal Render Pipeline", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.render-pipelines.universal@821b8547a8a5", "fingerprint": "821b8547a8a51a98545ccd08b604792e09f99215", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.test-framework@e9eb633386e7", "fingerprint": "e9eb633386e74717a4978296e116538a4051e958", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.textmeshpro@5.0.0": {"name": "com.unity.textmeshpro", "displayName": "TextMeshPro", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.textmeshpro@53f43edfd2e0", "fingerprint": "53f43edfd2e0bf2438ca6e2038ba12107a181253", "editorCompatibility": "2023.2.0a1", "version": "5.0.0", "source": "builtin", "testable": false}, "com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10": {"name": "com.unity.toolchain.win-x86_64-linux-x86_64", "displayName": "Toolchain Win Linux x64", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.toolchain.win-x86_64-linux-x86_64@426618737602", "fingerprint": "426618737602d0ff76e85db678b73b56aa2e891a", "editorCompatibility": "2019.4.0a1", "version": "2.0.10", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ugui@4f1c21fcc9bc", "fingerprint": "4f1c21fcc9bcee6987a89cc276da72ac77ce3db1", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.visualscripting@1.9.4": {"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.visualscripting@0920df93f957", "fingerprint": "0920df93f9574f857a55af27bd38fdd9230a3f51", "editorCompatibility": "2021.3.0a1", "version": "1.9.4", "source": "registry", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.sysroot@2.0.10": {"name": "com.unity.sysroot", "displayName": "Sysroot Base", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.sysroot@7bbbb9339c14", "fingerprint": "7bbbb9339c143f6fbccaeb0a52c77b9217b35a71", "editorCompatibility": "2019.4.0a1", "version": "2.0.10", "source": "registry", "testable": false}, "com.unity.sysroot.linux-x86_64@2.0.9": {"name": "com.unity.sysroot.linux-x86_64", "displayName": "Sysroot Linux x64", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.sysroot.linux-x86_64@1998d1c7730e", "fingerprint": "1998d1c7730e48fa49f28d983fececfc9cbd970b", "editorCompatibility": "2019.4.0a1", "version": "2.0.9", "source": "registry", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}, "com.unity.render-pipelines.core@17.1.0": {"name": "com.unity.render-pipelines.core", "displayName": "Scriptable Render Pipeline Core", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.render-pipelines.core@a2ee32414adf", "fingerprint": "a2ee32414adfc08513261803e2d3f62dcab7b8c2", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.1.0": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.shadergraph@e5d3455aa133", "fingerprint": "e5d3455aa13376f767ad6bf5f3faab2073877176", "editorCompatibility": "6000.1.0a1", "version": "17.1.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal Render Pipeline Config", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@8dc1aab4af1d", "fingerprint": "8dc1aab4af1d718781689a36ed5231a35ad1a524", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.nuget.newtonsoft-json@3.2.1": {"name": "com.unity.nuget.newtonsoft-json", "displayName": "Newtonsoft Json", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@74deb55db2a0", "fingerprint": "74deb55db2a0c29ddfda576608bcb86abbd13ee6", "editorCompatibility": "2018.4.0a1", "version": "3.2.1", "source": "registry", "testable": false}, "com.unity.sentis@2.1.2": {"name": "com.unity.sentis", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.sentis@c51693b52067", "fingerprint": "c51693b520678de462e1dbf1515869d9d63b6e96", "editorCompatibility": "2022.3.11f1", "version": "2.1.2", "source": "registry", "testable": false}, "com.unity.probuilder@6.0.5": {"name": "com.unity.probuilder", "displayName": "ProBuilder", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.probuilder@b55f3ad2afd2", "fingerprint": "b55f3ad2afd2f5505e0e532971da5bb5a17f128f", "editorCompatibility": "6000.0.0a1", "version": "6.0.5", "source": "registry", "testable": false}, "com.unity.polybrush@1.1.8": {"name": "com.unity.polybrush", "displayName": "Polybrush", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.polybrush@9b4531013fbd", "fingerprint": "9b4531013fbd4797d91318680619ee8780c62b91", "editorCompatibility": "2018.4.0a1", "version": "1.1.8", "source": "registry", "testable": false}, "com.unity.formats.fbx@5.1.3": {"name": "com.unity.formats.fbx", "displayName": "FBX Exporter", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.formats.fbx@db39de05b0db", "fingerprint": "db39de05b0dbacefb3aa3035d97d497649e2e711", "editorCompatibility": "2020.3.0a1", "version": "5.1.3", "source": "registry", "testable": false}, "com.unity.terrain-tools@5.2.1": {"name": "com.unity.terrain-tools", "displayName": "<PERSON><PERSON>ls", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.terrain-tools@332ce82b363c", "fingerprint": "332ce82b363c88ee8d8eb8688cab207a46abb6dc", "editorCompatibility": "2023.1.0a25", "version": "5.2.1", "source": "registry", "testable": false}, "com.unity.mobile.android-logcat@1.4.5": {"name": "com.unity.mobile.android-logcat", "displayName": "Android Logcat", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.mobile.android-logcat@0ddcd2133dc3", "fingerprint": "0ddcd2133dc3642d39aae1db273b1a54bed03644", "editorCompatibility": "2021.3.0a1", "version": "1.4.5", "source": "registry", "testable": false}, "com.unity.adaptiveperformance@5.1.4": {"name": "com.unity.adaptiveperformance", "displayName": "Adaptive Performance", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.adaptiveperformance@f25c04dfc305", "fingerprint": "f25c04dfc305fbce5593f78b1af66dfb77b75cb1", "editorCompatibility": "2022.3.0f1", "version": "5.1.4", "source": "registry", "testable": false}, "com.unity.mobile.notifications@2.4.1": {"name": "com.unity.mobile.notifications", "displayName": "Mobile Notifications", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.mobile.notifications@ec0aacc640b7", "fingerprint": "ec0aacc640b7d2e724393686de37e1602e8e2343", "editorCompatibility": "2021.3.0a1", "version": "2.4.1", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.ide.rider@3.0.36": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db", "fingerprint": "4d374c7eb6db6907c7e6925e3086c3c73f926e13", "editorCompatibility": "2019.4.6f1", "version": "3.0.36", "source": "registry", "testable": false}, "com.unity.editorcoroutines@1.0.0": {"name": "com.unity.editorcoroutines", "displayName": "Editor Coroutines", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.editorcoroutines@7d48783e7b8c", "fingerprint": "7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca", "editorCompatibility": "2018.1.0a1", "version": "1.0.0", "source": "registry", "testable": false}, "com.unity.performance.profile-analyzer@1.2.3": {"name": "com.unity.performance.profile-analyzer", "displayName": "Profile Analyzer", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.performance.profile-analyzer@a68e7bc84997", "fingerprint": "a68e7bc849973d943853204178d08a2bc7656ffe", "editorCompatibility": "2020.3.0a1", "version": "1.2.3", "source": "registry", "testable": false}, "com.unity.testtools.codecoverage@1.2.6": {"name": "com.unity.testtools.codecoverage", "displayName": "Code Coverage", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39", "fingerprint": "205a02cbcb39584f20b51c49b853047aceb3a3a7", "editorCompatibility": "2019.3.0a1", "version": "1.2.6", "source": "registry", "testable": false}, "com.unity.animation.rigging@1.3.0": {"name": "com.unity.animation.rigging", "displayName": "Animation Rigging", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.animation.rigging@68167b505d2b", "fingerprint": "68167b505d2b550a35f5d5b6daef6193de3c7460", "editorCompatibility": "2022.2.0a9", "version": "1.3.0", "source": "registry", "testable": false}, "com.unity.timeline@1.8.8": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.timeline@fa3a0bab2b90", "fingerprint": "fa3a0bab2b909389f8e6c20d5ff275c9e15ae0a2", "editorCompatibility": "2021.3.0a1", "version": "1.8.8", "source": "registry", "testable": false}, "com.unity.cinemachine@3.1.4": {"name": "com.unity.cinemachine", "displayName": "Cinemachine", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2", "fingerprint": "b66fdb7cd1f2f796860f420c11c2033b2262c986", "editorCompatibility": "2022.3.0a1", "version": "3.1.4", "source": "registry", "testable": false}, "com.unity.searcher@4.9.3": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.searcher@1e17ce91558d", "fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2", "editorCompatibility": "2019.1.0a1", "version": "4.9.3", "source": "registry", "testable": false}, "com.unity.burst@1.8.21": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.burst@59eb6f11d242", "fingerprint": "59eb6f11d2422f95682320d9daa3e79fdb076744", "editorCompatibility": "2020.3.0a1", "version": "1.8.21", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.rendering.light-transport@ec31b4120e30", "fingerprint": "ec31b4120e30d44c7f702fa7bfa50d70b562cd4a", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.autodesk.fbx@5.1.1": {"name": "com.autodesk.fbx", "displayName": "Autodesk FBX SDK for Unity", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7", "fingerprint": "5797ff6b31c7c66189bffe1adc9cbf0ad5a9cbbf", "editorCompatibility": "2020.3.0a1", "version": "5.1.1", "source": "registry", "testable": false}, "com.unity.settings-manager@2.1.0": {"name": "com.unity.settings-manager", "displayName": "Settings Manager", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.settings-manager@41738c275190", "fingerprint": "41738c27519039c335849eb78949382f4d7a3544", "editorCompatibility": "2022.3.0a1", "version": "2.1.0", "source": "registry", "testable": false}, "com.unity.profiling.core@1.0.2": {"name": "com.unity.profiling.core", "displayName": "Unity Profiling Core API", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.profiling.core@aac7b93912bc", "fingerprint": "aac7b93912bc5df5fe06b04ff1b758493cdc2346", "editorCompatibility": "2020.1.0a1", "version": "1.0.2", "source": "registry", "testable": false}, "com.unity.splines@2.8.1": {"name": "com.unity.splines", "displayName": "Splines", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.splines@b909627b5095", "fingerprint": "b909627b5095061d48761597bbb8384d6f04e510", "editorCompatibility": "2022.3.0a1", "version": "2.8.1", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "C:\\squadmateai\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}}}