{"name": "com.unity.cinemachine", "displayName": "Cinemachine", "version": "3.1.4", "unity": "2022.3", "description": "Smart camera tools for passionate creators. \n\nCinemachine 3 is a newer and better version of Cinemachine, but upgrading an existing project from 2.X will likely require some effort.  If you're considering upgrading an older project, please see our upgrade guide in the user manual.", "keywords": ["camera", "follow", "rig", "fps", "cinematography", "aim", "orbit", "cutscene", "cinematic", "collision", "freelook", "cinemachine", "compose", "composition", "dolly", "track", "clearshot", "noise", "framing", "handheld", "lens", "impulse"], "category": "cinematography", "dependencies": {"com.unity.splines": "2.0.0", "com.unity.modules.imgui": "1.0.0"}, "samples": [{"displayName": "2D Samples", "description": "Sample scenes illustrating various ways to use Cinemachine in a 2D setting", "path": "Samples~/2D Samples"}, {"displayName": "3D Samples", "description": "Sample scenes illustrating various ways to use Cinemachine in a 3D setting", "path": "Samples~/3D Samples"}, {"displayName": "Input System Samples", "description": "Sample scenes illustrating various ways to use Cinemachine with the input system.", "path": "Samples~/Input System Samples"}], "relatedPackages": {"com.unity.cinemachine.tests": "3.1.4"}, "_upm": {"changelog": "### Bugfixes\n- <PERSON><PERSON><PERSON><PERSON><PERSON> did not always properly reset its state.\n- <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> were introducing spurious damping when the FreeLook orbit size changed.\n- Mac only: Some dropdowns and popups in Cinemachine inspectors did not work consistently.\n- Regression fix: Confiner2D was not always confining when a camera was newly activated.\n- RotationComposer and PositionComposer no longer damp in response to composition changes from the FreeLookModifier.\n- Fixed sample asset selection at import depending on current render pipeline and input configuration.  You need to re-import the samples to fix any existing issues.\n- Confiner2D was behaving inconsistently for large sized bounding boxes due to precision issues.\n- StateDrivenCamera inspector was not populating the states in the instruction list correctly.\n- Fixed a number of issues causing ForceCameraPosition to not behave as expected.\n- Maximizing an inspector which contained custom blends would generate console errors and cause the inspector to be unresponsive.\n\n### Changed\n- Cinemachine Shot Editor for Timeline no longer displays improper UX to create cameras when editing a prefab.\n- The game-view composer guides dynamically reflect the current composition when a FreeLookModifier is changing it.\n- When a FreeLookModifier is enabled for composition, the game-view composer guides are no longer draggable.\n \n### Added\n- Added Easing setting to FreeLookModifier, to smooth the transition between top and bottom FreeLook hemispheres.\n- Added an overload for `CinemachineBrain.ManualUpdate` which takes a custom frame count and deltaTime, allowing more customized control over the game loop.\n- Added `CINEMACHINE_TRANSPARENT_POST_PROCESSING_BLENDS` scripting define to tune the PostProcessing blend behaviour.\n- Added Signal Combination Mode to Impulse Listener, to allow combining multiple impulse signals in different ways."}, "upmCi": {"footprint": "b8f4b0ff24373c6ab79f9e2ad7ef68dc4ee40117"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.cinemachine@3.1/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.cinemachine.git", "type": "git", "revision": "b5d928f5923583585b11d5c17531ff59c8c971c9"}, "_fingerprint": "b66fdb7cd1f2f796860f420c11c2033b2262c986"}