{"name": "com.unity.formats.fbx", "displayName": "FBX Exporter", "version": "5.1.3", "dependencies": {"com.unity.timeline": "1.7.1", "com.autodesk.fbx": "5.1.1"}, "unity": "2020.3", "description": "The FBX Exporter package enables round-trip workflows between Unity and 3D modeling software. Send geometry, Lights, Cameras, and animation from Unity to Autodesk® Maya®, Autodesk® Maya LT™, or Autodesk® 3ds Max®, and back to Unity again, with minimal effort.", "keywords": ["fbx", "animation", "modeling", "maya", "max"], "relatedPackages": {"com.unity.formats.fbx.tests": "5.1.3"}, "_upm": {"changelog": "### Fixed\n- Fixed an indentation issue in FBX Exporter Project Settings that was preventing straightforward toggling of checkboxes."}, "upmCi": {"footprint": "a1671fb7eb75d47a0caf462d6d0d2cf71066f130"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.formats.fbx@5.1/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.formats.fbx.git", "type": "git", "revision": "1dc43fa4b17f9e2d73204dbdd2463d060ddeb36c"}, "_fingerprint": "db39de05b0dbacefb3aa3035d97d497649e2e711"}