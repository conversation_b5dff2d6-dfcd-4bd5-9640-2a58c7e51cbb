using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Simple integration tests for all compilation fixes working together
    /// </summary>
    public class SimpleIntegrationTests : MonoBehaviour
    {
        [ContextMenu("Test All Compilation Fixes Together")]
        public void TestAllCompilationFixesTogether()
        {
            Debug.Log("🧪 Testing all compilation fixes working together...");
            
            try
            {
                // Create all test objects
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                // Test 1: PlayerController.isDowned
                bool playerTest = TestPlayerControllerIntegration(playerController);
                
                // Test 2: SquadMateAgent.hasWeapon
                bool agentTest = TestSquadMateAgentIntegration(agent);
                
                // Test 3: PUBGTrainingScenarios.SwitchToNextScenario
                bool scenarioTest = TestPUBGScenariosIntegration(scenarios);
                
                // Test 4: Cross-component communication
                bool communicationTest = TestCrossComponentCommunication(playerController, agent, scenarios);
                
                // Test 5: Debug.Log works
                Debug.Log("  Debug.Log capitalization test - this message confirms it works");
                bool debugTest = true;
                
                // Clean up
                DestroyImmediate(testPlayer);
                DestroyImmediate(testAgent);
                DestroyImmediate(testScenarios);
                
                // Evaluate overall results
                bool allTestsPassed = playerTest && agentTest && scenarioTest && communicationTest && debugTest;
                
                if (allTestsPassed)
                {
                    Debug.Log("🎉 ALL COMPILATION FIXES INTEGRATION TEST PASSED! 🎉");
                }
                else
                {
                    Debug.LogWarning("❌ Some integration tests failed");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Integration test ERROR: {e.Message}");
            }
        }
        
        private bool TestPlayerControllerIntegration(PlayerController playerController)
        {
            try
            {
                // Test that other components can access isDowned
                bool initialState = playerController.isDowned;
                playerController.GoDown();
                bool downedState = playerController.isDowned;
                
                Debug.Log($"  PlayerController integration: Initial={initialState}, Downed={downedState}");
                return !initialState && downedState;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  PlayerController integration failed: {e.Message}");
                return false;
            }
        }
        
        private bool TestSquadMateAgentIntegration(SquadMateAgent agent)
        {
            try
            {
                // Test that other components can access hasWeapon
                agent.hasWeapon = true;
                bool weaponState = agent.hasWeapon;
                
                Debug.Log($"  SquadMateAgent integration: HasWeapon={weaponState}");
                return weaponState;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  SquadMateAgent integration failed: {e.Message}");
                return false;
            }
        }
        
        private bool TestPUBGScenariosIntegration(PUBGTrainingScenarios scenarios)
        {
            try
            {
                // Test that other components can call SwitchToNextScenario
                PUBGScenarioType initialScenario = scenarios.currentScenario;
                scenarios.SwitchToNextScenario();
                
                Debug.Log($"  PUBGScenarios integration: Scenario switching works");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  PUBGScenarios integration failed: {e.Message}");
                return false;
            }
        }
        
        private bool TestCrossComponentCommunication(PlayerController player, SquadMateAgent agent, PUBGTrainingScenarios scenarios)
        {
            try
            {
                // Simulate real-world usage scenarios
                
                // Scenario 1: Agent checks if player is downed
                player.GoDown();
                bool canAgentSeePlayerDowned = player.isDowned;
                
                // Scenario 2: Controller checks if agent has weapon
                agent.hasWeapon = true;
                bool canControllerSeeAgentWeapon = agent.hasWeapon;
                
                // Scenario 3: Master controller switches scenarios
                scenarios.SwitchToNextScenario();
                
                Debug.Log($"  Cross-component communication: Player downed check={canAgentSeePlayerDowned}, Agent weapon check={canControllerSeeAgentWeapon}");
                return canAgentSeePlayerDowned && canControllerSeeAgentWeapon;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  Cross-component communication failed: {e.Message}");
                return false;
            }
        }
        
        [ContextMenu("Test Performance Impact")]
        public void TestPerformanceImpact()
        {
            Debug.Log("🧪 Testing performance impact of fixes...");
            
            try
            {
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
                
                // Perform many operations
                for (int i = 0; i < 1000; i++)
                {
                    bool _ = playerController.isDowned;
                    bool __ = agent.hasWeapon;
                    if (i % 100 == 0)
                    {
                        scenarios.SwitchToNextScenario();
                    }
                }
                
                stopwatch.Stop();
                
                DestroyImmediate(testPlayer);
                DestroyImmediate(testAgent);
                DestroyImmediate(testScenarios);
                
                Debug.Log($"  Performance test: 1000 operations completed in {stopwatch.ElapsedMilliseconds}ms");
                
                if (stopwatch.ElapsedMilliseconds < 100)
                {
                    Debug.Log("✅ Performance impact test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ Performance impact test FAILED - operations took too long");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Performance test ERROR: {e.Message}");
            }
        }
    }
}
