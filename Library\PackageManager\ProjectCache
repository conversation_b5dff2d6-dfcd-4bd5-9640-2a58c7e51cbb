m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: C:/squadmateai/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638852965884849585
    m_Hash: 2568259721
  m_LockFileStatus:
    m_FilePath: C:/squadmateai/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638852965884923279
    m_Hash: 2672908990
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: C:/squadmateai/Packages
m_EditorVersion: 6000.1.6f1 (d64b1a599cad)
m_ResolvedPackages:
- packageId: com.unity.ai.navigation@2.0.8
  testable: 0
  isDirectDependency: 1
  version: 2.0.8
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ai.navigation@eb5635ad590d
  assetPath: Packages/com.unity.ai.navigation
  name: com.unity.ai.navigation
  displayName: AI Navigation
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The AI Navigation package contains high-level components that allow
    you to use navmeshes to incorporate navigation and pathfinding in your game.
    With this package installed you can build and use navmeshes at runtime and at
    edit time, create dynamic obstacles, and use links to allow specific actions
    (like jumping) as your characters navigate between navmeshes.
  errors: []
  versions:
    all:
    - 1.0.0-exp.2
    - 1.0.0-exp.3
    - 1.0.0-exp.4
    - 1.1.0-pre.1
    - 1.1.0-pre.2
    - 1.1.1
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 2.0.0-pre.3
    - 2.0.0-pre.4
    - 2.0.0
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.7
    - 2.0.8
    compatible:
    - 2.0.7
    - 2.0.8
    recommended: 2.0.8
    deprecated: []
  dependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  keywords:
  - carving
  - mesh
  - navigation
  - navmesh
  - NavMesh
  - navmesh agent
  - navmesh link
  - navmeshlink
  - navmesh modifier
  - navmeshmodifier
  - navmesh modifier volume
  - navmeshmodifiervolume
  - navmesh obstacle
  - navmesh surface
  - navmeshsurface
  - offmesh link
  - off-mesh link
  - pathfinding
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638841129733020000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.navigation@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ai.navigation.git
    revision: edeb8e889d1eedf4001fa41bdab7d69d01a62f24
    path: 
  unityLifecycle:
    version: 2.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Samples will now use appropriate material
    colors in projects using HDRP or URP. Install the **com.unity.shadergraph** package
    to show the materials correctly for built-in render pipeline projects."}'
  assetStore:
    productId: 
  fingerprint: eb5635ad590d47cef2a5c920d9475cc222db3f67
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.7
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.collab-proxy@2.5.1
  testable: 0
  isDirectDependency: 1
  version: 2.5.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.collab-proxy@c810163b1645
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: 
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    compatible:
    - 2.8.2
    recommended: 2.8.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638612183804560000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: 470a7a1321fe499cf5055d6b1d64da3a08c8dcbc
    path: 
  unityLifecycle:
    version: 2.8.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Allow deletion of non-empty branches
    that have not been merged anywhere\n- New setting to control if new files must
    be automatically added to source control\n\n### Changed\n\n- Improve the dialog
    to install Unity Version Control from the Editor\n\n### Fixed\n\n- Fixed ''an
    existing xxx operation has locked the workspace'' error on finding changes operation\n-
    Fixed a case of a hang happening on Editor quit\n- Fixed the login that could
    fail for Enterprise installation\n- Fixed undo operation that didn''t capture
    changes in packages\n- Fixed history that was not shown when there was no desktop
    client installed\n- Fixed unity 6 Editor going into the background after a dialog
    was closed\n- Fixed wrong font size when changing to changesets view\n- Fixed
    the \"Upgrade plan\" button in Unity plugin that used a obsolete redirection
    link\n- Fixed Logs that were configured too late in the initialization\n- Fixed
    the minimum supported version (2020.3.48f1 LTS) in the README"}'
  assetStore:
    productId: 
  fingerprint: c810163b1645af64e676552b0e8d0b3c575991e6
  editorCompatibility:
    compatibilityLevel: 1
    minimumPackageVersion: 2.8.2
    minimumUnityVersion: 2020.3.48f1
- packageId: com.unity.feature.characters-animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.feature.characters-animation@62cc2ae02a69
  assetPath: Packages/com.unity.feature.characters-animation
  name: com.unity.feature.characters-animation
  displayName: 3D Characters and Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: 'Animate characters and create cutscenes with the Characters and Animation
    feature set.


    Build interactive character rigs and constraints to animate
    characters and GameObjects.

    Arrange tracks and clips to create gameplay
    sequences, cutscenes, or movies.

    Orchestrate, shoot, and record beautiful
    narratives for cutscenes or movies.

    Easily round-trip to your 3D modeling
    software'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.animation.rigging
    version: default
  - name: com.unity.timeline
    version: default
  - name: com.unity.cinemachine
    version: default
  - name: com.unity.formats.fbx
    version: default
  resolvedDependencies:
  - name: com.unity.animation.rigging
    version: 1.3.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.timeline
    version: 1.8.8
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  - name: com.unity.cinemachine
    version: 3.1.4
  - name: com.unity.splines
    version: 2.8.1
  - name: com.unity.settings-manager
    version: 2.1.0
  - name: com.unity.formats.fbx
    version: 5.1.3
  - name: com.autodesk.fbx
    version: 5.1.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/CharacterAnimationFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 62cc2ae02a691f0bf7ba8b57ffbef685bcfa0902
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.feature.development@1.0.2
  testable: 0
  isDirectDependency: 1
  version: 1.0.2
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.feature.development@767aadbc6eb7
  assetPath: Packages/com.unity.feature.development
  name: com.unity.feature.development
  displayName: Engineering
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: "Optimize your development experience in Unity with the Dev Tools
    feature set. Enable support for multiple integrated development environments
    (IDE) for editing your Unity code. Get access to development tools to help you
    test and analyze your project\u2019s performance."
  errors: []
  versions:
    all:
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies:
  - name: com.unity.ide.visualstudio
    version: default
  - name: com.unity.ide.rider
    version: default
  - name: com.unity.editorcoroutines
    version: default
  - name: com.unity.performance.profile-analyzer
    version: default
  - name: com.unity.test-framework
    version: default
  - name: com.unity.testtools.codecoverage
    version: default
  resolvedDependencies:
  - name: com.unity.ide.visualstudio
    version: 2.0.23
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ide.rider
    version: 3.0.36
  - name: com.unity.editorcoroutines
    version: 1.0.0
  - name: com.unity.performance.profile-analyzer
    version: 1.2.3
  - name: com.unity.testtools.codecoverage
    version: 1.2.6
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/DeveloperToolsFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 767aadbc6eb72681a4ca807c8fa248e0230a0cef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 
- packageId: com.unity.feature.mobile@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.feature.mobile@00f8a2b6a6fb
  assetPath: Packages/com.unity.feature.mobile
  name: com.unity.feature.mobile
  displayName: Mobile
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: Optimize your apps for mobile devices and provide a performant experience
    for your users. Includes utilities for debugging on device and support for notifications.
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.mobile.android-logcat
    version: default
  - name: com.unity.adaptiveperformance
    version: default
  - name: com.unity.mobile.notifications
    version: default
  resolvedDependencies:
  - name: com.unity.mobile.android-logcat
    version: 1.4.5
  - name: com.unity.adaptiveperformance
    version: 5.1.4
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.modules.subsystems
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.mobile.notifications
    version: 2.4.1
  - name: com.unity.modules.androidjni
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/MobileFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 00f8a2b6a6fb9c01b3e6d2ada669d3dc43945443
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.feature.worldbuilding@1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.feature.worldbuilding@d247f3a881a3
  assetPath: Packages/com.unity.feature.worldbuilding
  name: com.unity.feature.worldbuilding
  displayName: 3D World Building
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: Create attractive 3D environments with the World building feature
    set and round-trip assets between your 3D modeling software of choice using FBX
    to realize your creative intent. Create geometry, sculpt meshes, & terrain, blend
    textures, and place objects throughout your scene using artist driven tools to
    create believable interactive environments.
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.probuilder
    version: default
  - name: com.unity.polybrush
    version: default
  - name: com.unity.formats.fbx
    version: default
  - name: com.unity.terrain-tools
    version: default
  resolvedDependencies:
  - name: com.unity.probuilder
    version: 6.0.5
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.settings-manager
    version: 2.1.0
  - name: com.unity.polybrush
    version: 1.1.8
  - name: com.unity.formats.fbx
    version: 5.1.3
  - name: com.autodesk.fbx
    version: 5.1.1
  - name: com.unity.timeline
    version: 1.8.8
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  - name: com.unity.terrain-tools
    version: 5.2.1
  - name: com.unity.modules.terrainphysics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/WorldBuildingFeature.html"}'
  assetStore:
    productId: 
  fingerprint: d247f3a881a38e10db11c29399be05dc6f27b501
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 
- packageId: com.unity.ide.vscode@1.2.5
  testable: 0
  isDirectDependency: 1
  version: 1.2.5
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ide.vscode@82e68656b7bd
  assetPath: Packages/com.unity.ide.vscode
  name: com.unity.ide.vscode
  displayName: Visual Studio Code Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: Code editor integration for supporting Visual Studio Code as code
    editor for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.7
    - 1.1.0
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    compatible:
    - 1.0.2
    - 1.0.3
    - 1.0.7
    - 1.1.0
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637800255360000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.ide.vscode.git
    revision: b0740c80bfc2440527c317109f7c3d9100132722
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 1
    deprecationMessage: Visual Studio Code package is not supported anymore. You
      can continue to use it, but we won't provide any update anymore.
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 82e68656b7bdc5da8beb1fbaf1bb4ecf13456d51
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 2019.2.0a12
- packageId: com.unity.ml-agents@3.0.0
  testable: 0
  isDirectDependency: 1
  version: 3.0.0
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ml-agents@2b4ded88494d
  assetPath: Packages/com.unity.ml-agents
  name: com.unity.ml-agents
  displayName: ML Agents
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Use state-of-the-art machine learning to create intelligent character
    behaviors in any Unity environment (games, robotics, film, etc.).
  errors: []
  versions:
    all:
    - 0.16.0-preview
    - 1.0.0-preview
    - 1.0.1-preview
    - 1.0.2-preview
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.5
    - 1.0.6
    - 1.0.7
    - 1.0.8
    - 1.1.0-preview
    - 1.2.0-preview
    - 1.3.0-preview
    - 1.4.0-preview
    - 1.5.0-preview
    - 1.6.0-preview
    - 1.7.0-preview
    - 1.7.2-preview
    - 1.8.0-preview
    - 1.8.1-preview
    - 1.9.0-preview
    - 1.9.1-preview
    - 2.0.0-exp.1
    - 2.0.0-pre.3
    - 2.0.0
    - 2.0.1
    - 2.1.0-exp.1
    - 2.2.1-exp.1
    - 2.3.0-exp.2
    - 2.3.0-exp.3
    - 3.0.0-exp.1
    - 3.0.0
    compatible:
    - 2.0.1
    - 2.1.0-exp.1
    - 2.2.1-exp.1
    - 2.3.0-exp.2
    - 2.3.0-exp.3
    - 3.0.0-exp.1
    - 3.0.0
    recommended: 2.0.1
    deprecated: []
  dependencies:
  - name: com.unity.sentis
    version: 2.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.sentis
    version: 2.1.2
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638637322569190000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ml-agents@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/ml-agents.git
    revision: 501ba880cc047f3efe0b140b6908ce26fa12b382
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Major Changes\n#### com.unity.ml-agents / com.unity.ml-agents.extensions
    (C#)\n- Upgraded to Sentis 2.1.0 ()\n- Upgraded to Sentis 2.0.0 (#6137)\n- Upgraded
    to Sentis 1.3.0-pre.3 (#6070)\n- Upgraded to Sentis 1.3.0-exp.2 (#6013)\n- The
    minimum supported Unity version was updated to 2023.2. (#6071)\n\n#### ml-agents
    / ml-agents-envs\n- Upgraded to PyTorch 2.1.1. (#6013)\n\n### Minor Changes\n####
    com.unity.ml-agents / com.unity.ml-agents.extensions (C#)\n- Added no-graphics-monitor.
    (#6014)\n\n#### ml-agents / ml-agents-envs\n- Update Installation.md (#6004)\n-
    Updated Using-Virtual-Environment.md (#6033)\n\n### Bug Fixes\n#### com.unity.ml-agents
    / com.unity.ml-agents.extensions (C#)\n- Fix failing ci post upgrade (#6141)\n-
    Fixed missing assembly reference for google protobuf. (#6099)\n- Fixed missing
    tensor Dispose in ModelRunner. (#6028)\n- Fixed 3DBall sample package to remove
    Barracuda dependency. (#6030)\n\n#### ml-agents / ml-agents-envs\n- Fix sample
    code indentation in migrating.md (#5840)\n- Fixed continuous integration tests
    (#6079)\n- Fixed bad like format (#6078)\n- Bumped numpy version to >=1.23.5,<1.24.0
    (#6082)\n- Bumped onnx version to 1.15.0 (#6062)\n- Bumped protobuf version to
    >=3.6,<21 (#6062)"}'
  assetStore:
    productId: 
  fingerprint: 2b4ded88494d1338cea1b3eb7364ff4dfe142399
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.1
    minimumUnityVersion: 2023.2.0a1
- packageId: com.unity.multiplayer.playmode@1.4.3
  testable: 0
  isDirectDependency: 1
  version: 1.4.3
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.multiplayer.playmode@50e3dba5f9d6
  assetPath: Packages/com.unity.multiplayer.playmode
  name: com.unity.multiplayer.playmode
  displayName: Multiplayer Play Mode
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Multiplayer Play Mode is a feature that enables multiple editor instances
    to be opened simultaneously on the same development device.
  errors: []
  versions:
    all:
    - 0.1.0
    - 0.1.1
    - 0.2.0
    - 0.3.0
    - 0.4.0
    - 0.5.0
    - 0.6.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.1.0
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.3.0-exp.1
    - 1.3.0-exp.2
    - 1.3.0-exp.3
    - 1.3.0-exp.4
    - 1.3.0-pre.1
    - 1.3.0-pre.2
    - 1.3.0-pre.3
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.4.0-pre.1
    - 1.4.0-pre.2
    - 1.4.0-pre.3
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.5.0-exp.1
    - 1.5.0-exp.2
    - 1.5.0-exp.3
    - 1.5.0-exp.4
    - 1.5.0
    compatible:
    - 1.4.2
    - 1.4.3
    - 1.5.0-exp.1
    - 1.5.0-exp.2
    - 1.5.0-exp.3
    - 1.5.0-exp.4
    - 1.5.0
    recommended: 1.4.3
    deprecated: []
  dependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 2.0.2
  resolvedDependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638827659614750000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.multiplayer.playmode@1.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/multiplayer-workflows.git
    revision: b9f0032e9cb6af582a1cc5fd03547d45db6ee7a3
    path: 
  unityLifecycle:
    version: 1.4.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: "{\"changelog\":\"### Fixed \\n- Using non authorized characters when
    creating a new Scenario configuration is now returning a warning rather than
    an error. \\n- Fixed an issue where toggling Simulator mode in the Virtual Player's
    Game Window fails to show\\n- Removed the minimum height restriction on PlayModePopupContentWindow
    to prevent excessive height when only one or two scenario configs are present\\n-
    Fixed low-resolution icons in various windows\\n- Set a minimum width for the
    scenario config list in the Scenario Config window to prevent resizing it below
    a usable size\\n- Added an info HelpBox that appears when no scenario is selected,
    preventing the window from appearing blank\\n- Updated the Multiplayer role dropdown
    to display \u201CClient And Server\u201D with proper spacing in the Scenario
    Config window\\n- Fixed an issue where icons did not adapt when switching between
    dark and light mode in the editor in PlayModePopupContentWindow and PlayModeStatusWindow\"}"
  assetStore:
    productId: 
  fingerprint: 50e3dba5f9d669a30d286824f8656452f51a6790
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.4.2
    minimumUnityVersion: 6000.1.0b1
- packageId: com.unity.render-pipelines.universal@17.1.0
  testable: 0
  isDirectDependency: 1
  version: 17.1.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal Render Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  errors: []
  versions:
    all:
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.35
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.26
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 821b8547a8a51a98545ccd08b604792e09f99215
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.test-framework@1.5.1
  testable: 0
  isDirectDependency: 1
  version: 1.5.1
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.test-framework@e9eb633386e7
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.5.1
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e9eb633386e74717a4978296e116538a4051e958
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.1
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.textmeshpro@5.0.0
  testable: 0
  isDirectDependency: 1
  version: 5.0.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.textmeshpro@53f43edfd2e0
  assetPath: Packages/com.unity.textmeshpro
  name: com.unity.textmeshpro
  displayName: TextMeshPro
  author:
    name: 
    email: 
    url: 
  category: Text Rendering
  type: shim
  description: This package is no longer supported. TextMeshPro functionalities are
    now included in the com.unity.ugui package. To continue using them, consider
    adding it to your project dependencies.
  errors: []
  versions:
    all:
    - 0.1.2
    - 1.0.21
    - 1.0.23
    - 1.0.25
    - 1.0.26
    - 1.1.0
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.0-preview
    - 1.3.0
    - 1.4.0-preview.1b
    - 1.4.0-preview.2a
    - 1.4.0-preview.3a
    - 1.4.0
    - 1.4.1-preview.1
    - 1.4.1
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.0-preview.6
    - 1.5.0-preview.7
    - 1.5.0-preview.8
    - 1.5.0-preview.10
    - 1.5.0-preview.11
    - 1.5.0-preview.12
    - 1.5.0-preview.13
    - 1.5.0-preview.14
    - 1.5.0
    - 1.5.1
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.6.0-preview.1
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.1
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.3
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.1.0-preview.8
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 2.1.0-preview.12
    - 2.1.0-preview.13
    - 2.1.0-preview.14
    - 2.1.0
    - 2.1.1
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.2.0-preview.1
    - 2.2.0-preview.2
    - 2.2.0-preview.3
    - 3.0.0-preview.1
    - 3.0.0-preview.3
    - 3.0.0-preview.4
    - 3.0.0-preview.5
    - 3.0.0-preview.7
    - 3.0.0-preview.8
    - 3.0.0-preview.10
    - 3.0.0-preview.11
    - 3.0.0-preview.12
    - 3.0.0-preview.13
    - 3.0.0-preview.14
    - 3.0.0
    - 3.0.1
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.2.0-pre.1
    - 3.2.0-pre.2
    - 3.2.0-pre.3
    - 3.2.0-pre.4
    - 3.2.0-pre.5
    - 3.2.0-pre.6
    - 3.2.0-pre.7
    - 3.2.0-pre.8
    - 3.2.0-pre.9
    - 3.2.0-pre.10
    - 3.2.0-pre.11
    - 3.2.0-pre.12
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 5.0.0
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.ugui
    version: 2.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 1
    deprecationMessage: This package is no longer supported. TextMeshPro functionalities
      are now included in the com.unity.ugui package. To continue using them, consider
      adding it to your project dependencies.
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 53f43edfd2e0bf2438ca6e2038ba12107a181253
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2023.2.0a1
- packageId: com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10
  testable: 0
  isDirectDependency: 1
  version: 2.0.10
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.toolchain.win-x86_64-linux-x86_64@426618737602
  assetPath: Packages/com.unity.toolchain.win-x86_64-linux-x86_64
  name: com.unity.toolchain.win-x86_64-linux-x86_64
  displayName: Toolchain Win Linux x64
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Cross-compilation toolchain to build player target Linux x86_64 on
    host Windows x86_64
  errors: []
  versions:
    all:
    - 0.1.4-preview
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.7-preview
    - 0.1.8-preview
    - 0.1.9-preview
    - 0.1.10-preview
    - 0.1.11-preview
    - 0.1.13-preview
    - 0.1.14-preview
    - 0.1.15-preview
    - 0.1.16-preview
    - 0.1.17-preview
    - 0.1.18-preview
    - 0.1.19-preview
    - 0.1.20-preview
    - 0.1.21-preview
    - 1.0.0
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.9
    - 2.0.10
    compatible:
    - 2.0.10
    recommended: 2.0.10
    deprecated: []
  dependencies:
  - name: com.unity.sysroot
    version: 2.0.10
  - name: com.unity.sysroot.linux-x86_64
    version: 2.0.9
  resolvedDependencies:
  - name: com.unity.sysroot
    version: 2.0.10
  - name: com.unity.sysroot.linux-x86_64
    version: 2.0.9
  keywords:
  - toolchain
  - windows
  - linux
  - cross-compilation
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638651066000810000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.toolchain.win-x86_64-linux-x86_64@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.sysroot.git
    revision: c5a32c15f32b7de0b0aa81c256ea52b322478b15
    path: 
  unityLifecycle:
    version: 2.0.10
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Optimized and reduced package size"}'
  assetStore:
    productId: 
  fingerprint: 426618737602d0ff76e85db678b73b56aa2e891a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.10
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ugui@4f1c21fcc9bc
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    recommended: 2.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 4f1c21fcc9bcee6987a89cc276da72ac77ce3db1
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.visualscripting@1.9.4
  testable: 0
  isDirectDependency: 1
  version: 1.9.4
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.visualscripting@0920df93f957
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638488567680000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 41aac2b7038c2b1259c546329890d3736ce2394e
    path: 
  unityLifecycle:
    version: 1.9.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed sqlite dll changes not being recognized
    correctly by the 2022.3 Unity Editor"}'
  assetStore:
    productId: 
  fingerprint: 0920df93f9574f857a55af27bd38fdd9230a3f51
  editorCompatibility:
    compatibilityLevel: 1
    minimumPackageVersion: 1.9.7
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.sysroot@2.0.10
  testable: 0
  isDirectDependency: 0
  version: 2.0.10
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.sysroot@7bbbb9339c14
  assetPath: Packages/com.unity.sysroot
  name: com.unity.sysroot
  displayName: Sysroot Base
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: This is the base sysroot package required by all sysroot and toolchain
    packages
  errors: []
  versions:
    all:
    - 0.1.7-preview
    - 0.1.8-preview
    - 0.1.9-preview
    - 0.1.10-preview
    - 0.1.11-preview
    - 0.1.12-preview
    - 0.1.14-preview
    - 0.1.15-preview
    - 0.1.16-preview
    - 0.1.17-preview
    - 0.1.18-preview
    - 0.1.19-preview
    - 1.0.0
    - 2.0.0
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.7
    - 2.0.10
    compatible:
    - 2.0.10
    recommended: 2.0.10
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  - linux
  - sysroot
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638500638510000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.sysroot@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.sysroot.git
    revision: 48e503c8f30007e813fbcec90dc84795285406b4
    path: 
  unityLifecycle:
    version: 2.0.10
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"bump version number to publish"}'
  assetStore:
    productId: 
  fingerprint: 7bbbb9339c143f6fbccaeb0a52c77b9217b35a71
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.10
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.sysroot.linux-x86_64@2.0.9
  testable: 0
  isDirectDependency: 0
  version: 2.0.9
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.sysroot.linux-x86_64@1998d1c7730e
  assetPath: Packages/com.unity.sysroot.linux-x86_64
  name: com.unity.sysroot.linux-x86_64
  displayName: Sysroot Linux x64
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Sysroot used to build unity player for linux x86_64
  errors: []
  versions:
    all:
    - 0.1.6-preview
    - 0.1.7-preview
    - 0.1.8-preview
    - 0.1.9-preview
    - 0.1.10-preview
    - 0.1.11-preview
    - 0.1.12-preview
    - 0.1.13-preview
    - 0.1.14-preview
    - 0.1.15-preview
    - 1.0.0
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.9
    compatible:
    - 2.0.9
    recommended: 2.0.9
    deprecated: []
  dependencies:
  - name: com.unity.sysroot
    version: 2.0.10
  resolvedDependencies:
  - name: com.unity.sysroot
    version: 2.0.10
  keywords:
  - unity
  - linux
  - sysroot
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638500638600000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.sysroot.linux-x86_64@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.sysroot.git
    revision: bf7a008256a1175e0f8a0d7d26d6826cbfbe1bb2
    path: 
  unityLifecycle:
    version: 2.0.9
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"bump version number to publish"}'
  assetStore:
    productId: 
  fingerprint: 1998d1c7730e48fa49f28d983fececfc9cbd970b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.9
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ext.nunit@031a54704bff
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 031a54704bffe39e6a0324909f8eaa4565bdebf2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.render-pipelines.core@17.1.0
  testable: 0
  isDirectDependency: 0
  version: 17.1.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.render-pipelines.core@a2ee32414adf
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Scriptable Render Pipeline Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 0.1.21
    - 0.1.27
    - 0.1.28
    - 1.0.0-beta
    - 1.0.1-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.8-preview
    - 1.1.10-preview
    - 1.1.11-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.35
    - 9.0.0-preview.38
    - 9.0.0-preview.60
    - 9.0.0-preview.77
    - 10.0.0-preview.30
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.14
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a2ee32414adfc08513261803e2d3f62dcab7b8c2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.shadergraph@17.1.0
  testable: 0
  isDirectDependency: 0
  version: 17.1.0
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.shadergraph@e5d3455aa133
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 0.1.8
    - 0.1.9
    - 0.1.17
    - 1.0.0-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.6-preview
    - 1.1.8-preview
    - 1.1.9-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.34
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.searcher
    version: 4.9.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e5d3455aa13376f767ad6bf5f3faab2073877176
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.render-pipelines.universal-config@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.render-pipelines.universal-config@8dc1aab4af1d
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal Render Pipeline Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  errors: []
  versions:
    all:
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 8dc1aab4af1d718781689a36ed5231a35ad1a524
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.nuget.newtonsoft-json@3.2.1
  testable: 0
  isDirectDependency: 0
  version: 3.2.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0
  assetPath: Packages/com.unity.nuget.newtonsoft-json
  name: com.unity.nuget.newtonsoft-json
  displayName: Newtonsoft Json
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'Newtonsoft Json for use in Unity projects and Unity packages. Currently
    synced to version 13.0.2.


    This package is used for advanced json serialization
    and deserialization. Most Unity users will be better suited using the existing
    json tools built into Unity.

    To avoid assembly clashes, please use this
    package if you intend to use Newtonsoft Json.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.1-preview.1
    - 1.1.2
    - 2.0.0-preview
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.2
    - 3.0.1
    - 3.0.2
    - 3.1.0
    - 3.2.0
    - 3.2.1
    compatible:
    - 3.2.1
    recommended: 3.2.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 638186318800000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
    revision: d8e49aef8979bef617144382052ec2f479645eaf
    path: 
  unityLifecycle:
    version: 3.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Fixed Newtonsoft DLL when compiling with netstandard
    2.0."}'
  assetStore:
    productId: 
  fingerprint: 74deb55db2a0c29ddfda576608bcb86abbd13ee6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.2.1
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.sentis@2.1.2
  testable: 0
  isDirectDependency: 0
  version: 2.1.2
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.sentis@c51693b52067
  assetPath: Packages/com.unity.sentis
  name: com.unity.sentis
  displayName: Sentis
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Sentis is a neural network inference library. It enables you to import
    trained neural network models, connect the network inputs and outputs to your
    game code, and then run them locally in your end-user app. Use cases include
    capabilities like natural language processing, object recognition, automated
    game opponents, sensor data classification, and many more.


    Sentis automatically
    optimizes your network for real-time use to speed up inference. It also allows
    you to tune your implementation further with tools like frame slicing, quantization,
    and custom backend (i.e. compute type) dispatching.


    Visit https://unity.com/sentis
    for more resources.'
  errors: []
  versions:
    all:
    - 1.0.0-exp.6
    - 1.1.0-exp.2
    - 1.1.1-exp.2
    - 1.2.0-exp.2
    - 1.3.0-pre.1
    - 1.3.0-pre.2
    - 1.3.0-pre.3
    - 1.4.0-pre.1
    - 1.4.0-pre.2
    - 1.4.0-pre.3
    - 1.5.0-pre.2
    - 1.5.0-pre.3
    - 1.6.0-pre.1
    - 2.0.0
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 2.1.3
    compatible:
    - 2.1.2
    - 2.1.3
    recommended: 2.1.3
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638741825071190000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.sentis@2.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/UnityInferenceEngine.git
    revision: a3d9b8d1bf3e579ab0c6a21f5eafc91a3253faa3
    path: 
  unityLifecycle:
    version: 2.1.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Support for accelerated inference using
    [DirectML](https://learn.microsoft.com/en-us/windows/ai/directml/dml) in future
    Unity versions\n- Support for RMSNormalization subgraph optimization\n\n### Changed\n-
    Some methods in the TextureConverter have been made obsolete\n\n### Fixed\n-
    Improved CPU backend performance by optimizing Burst job count\n- Reduced memory
    usage when serializing and deserializing large models\n- Shader issues on XBOX
    hardware\n- GPUPixel now reuses RenderTextures reducing garbage collection"}'
  assetStore:
    productId: 
  fingerprint: c51693b520678de462e1dbf1515869d9d63b6e96
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.1.2
    minimumUnityVersion: 2022.3.11f1
- packageId: com.unity.probuilder@6.0.5
  testable: 0
  isDirectDependency: 0
  version: 6.0.5
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.probuilder@b55f3ad2afd2
  assetPath: Packages/com.unity.probuilder
  name: com.unity.probuilder
  displayName: ProBuilder
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Build, edit, and texture custom geometry in Unity. Use ProBuilder
    for in-scene level design, prototyping, collision meshes, all with on-the-fly
    play-testing.


    Advanced features include UV editing, vertex colors, parametric
    shapes, and texture blending. With ProBuilder''s model export feature it''s easy
    to tweak your levels in any external 3D modelling suite.'
  errors: []
  versions:
    all:
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.6
    - 3.0.8
    - 3.0.9
    - 3.10.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.0.5
    - 4.1.0
    - 4.1.2
    - 4.2.1
    - 4.2.2-preview.1
    - 4.2.2-preview.2
    - 4.2.3
    - 4.2.4-preview.0
    - 4.3.0-preview.0
    - 4.3.0-preview.1
    - 4.3.0-preview.2
    - 4.3.0-preview.4
    - 4.3.0-preview.6
    - 4.3.0-preview.7
    - 4.3.0-preview.8
    - 4.3.0-preview.9
    - 4.3.1
    - 4.4.0-preview.1
    - 4.4.0
    - 4.5.0
    - 4.5.2
    - 5.0.0-pre.7
    - 5.0.0-pre.10
    - 5.0.1
    - 5.0.3
    - 5.0.4
    - 5.0.6
    - 5.0.7
    - 5.1.0
    - 5.1.1
    - 5.2.0
    - 5.2.2
    - 5.2.3
    - 5.2.4
    - 6.0.1-pre.1
    - 6.0.1-pre.2
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    compatible:
    - 6.0.5
    recommended: 6.0.5
    deprecated: []
  dependencies:
  - name: com.unity.shadergraph
    version: 17.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 1.0.3
  resolvedDependencies:
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - 3d
  - model
  - mesh
  - modeling
  - geometry
  - shape
  - cube
  - blender
  - max
  - maya
  - fbx
  - obj
  - level
  - design
  - block
  - greybox
  - graybox
  - whitebox
  - prototype
  - probuilder
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638780358106590000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.probuilder@6.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.probuilder.git
    revision: 3efb5ffce15fb2092437e0cb5a15f3a5de69fb61
    path: 
  unityLifecycle:
    version: 6.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changes\n\n- Improved DrawShape tool performance
    when you place the first vertex.\n\n### Fixed\n\n- Fixed a bug where undoing
    a PolyShape creation then redoing it did not restore the PolyShape.\n- Fixed
    a bug where you could progress to the Edit Height stage of the Polyshape creation
    tool without forming a base polygon with at least three points.\n- Fixed a bug
    where shapes created by pressing Enter or Space using the Polyshape creation
    tool were not selectable or added to the scene hierarchy.\n- [PBLD-213] Fixed
    a bug where selection changes made while the PolyShape creation tool was active
    were being reverted.\n- [PBLD-192] Fixed a bug where vertices were not able to
    snap on other vertices from the same mesh.\n- [PBLD-189] Fixed a bug where using
    the auto-stitch functionality of the UV Editor would not work properly on MacOS.\n-
    [PBLD-187] Fixed a bug where the object size was incorrect when using the DrawShape
    tool on angled surfaces.\n- [PBLD-180] Fixed a bug in the Material Editor where
    an exception was thrown because it attempted to retrieve `MenuItem` shortcuts
    before the main menu was fully loaded.\n- [PBLD-210] Fixed a bug where Undo was
    not working correctly with the Polyshape creation tool. \n- [PBLD-196] Fixed
    a bug where the Polyshape creation tool was not working with an orthographic
    camera.\n- [STO-3429] Fixed a bug where increment snapping was ignored when drawing
    ProBuilder shapes.\n- [STO-3432] Fixed a bug where the Polyshape creation tool
    was not placing the first point on a custom grids correctly.\n- [PBLD-183] Fixed
    a bug where the **Extrude by** setting of the **Extrude Faces** action was always
    set to **Individual Faces**.\n- [STO-3442] Fixed a bug where hover-highlighted
    elements are not always selected.\n- [PBLD-208] Fixed an issue \"Apply Quick
    Material\" shortcut would always be present in the Shortcut Helper Bar.\n- [PBLD-202]
    Fixed an issue where creating ProBuilder shapes using the Hierarchy menu would
    not result in procedurally editable `ProBuilderShape` meshes. \n- [PBLD-205]
    Fixed a bug where the `Edit PolyShape` tool would revert previously merged PolyShape
    objects.\n- [PBLD-199] Fixed a bug where an exception was thrown if you placed
    two cut tool vertices on the same edge."}'
  assetStore:
    productId: 
  fingerprint: b55f3ad2afd2f5505e0e532971da5bb5a17f128f
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 6.0.5
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.polybrush@1.1.8
  testable: 0
  isDirectDependency: 0
  version: 1.1.8
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.polybrush@9b4531013fbd
  assetPath: Packages/com.unity.polybrush
  name: com.unity.polybrush
  displayName: Polybrush
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Mesh painting, sculpting, and geo-scattering tool for Unity.
  errors: []
  versions:
    all:
    - 1.0.0-preview.13
    - 1.0.0-preview.16
    - 1.0.0-preview.17
    - 1.0.0
    - 1.0.1-preview.1
    - 1.0.1-preview.2
    - 1.0.1
    - 1.0.2-preview.1
    - 1.0.2-preview.4
    - 1.0.2
    - 1.1.0-pre.1
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 1.1.8
    compatible:
    - 1.1.8
    recommended: 1.1.8
    deprecated: []
  dependencies:
  - name: com.unity.settings-manager
    version: 2.0.1
  resolvedDependencies:
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - modeling
  - sculpting
  - push
  - clay
  - terrain
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638567250100000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.polybrush@1.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/polybrush.git
    revision: 571b9c3665597c648dbe7c7dc7688804260a8ec0
    path: 
  unityLifecycle:
    version: 1.1.8
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bug Fixes\n\n- [POLBR-22] Fixed compilation errors
    related to the use of FindObjectsByType in affected editors."}'
  assetStore:
    productId: 
  fingerprint: 9b4531013fbd4797d91318680619ee8780c62b91
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.1.8
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.formats.fbx@5.1.3
  testable: 0
  isDirectDependency: 0
  version: 5.1.3
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.formats.fbx@db39de05b0db
  assetPath: Packages/com.unity.formats.fbx
  name: com.unity.formats.fbx
  displayName: FBX Exporter
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The FBX Exporter package enables round-trip workflows between Unity
    and 3D modeling software. Send geometry, Lights, Cameras, and animation from
    Unity to Autodesk\xAE Maya\xAE, Autodesk\xAE Maya LT\u2122, or Autodesk\xAE 3ds
    Max\xAE, and back to Unity again, with minimal effort."
  errors: []
  versions:
    all:
    - 2.0.1-preview.2
    - 2.0.1-preview.3
    - 2.0.1-preview.4
    - 2.0.1-preview.5
    - 2.0.1-preview.11
    - 2.0.2-preview.1
    - 2.0.3-preview.3
    - 3.0.0-preview.2
    - 3.0.1-preview.2
    - 3.1.0-preview.1
    - 3.2.0-preview.2
    - 3.2.1-preview.2
    - 4.0.0-pre.1
    - 4.0.0-pre.4
    - 4.0.1
    - 4.1.0-pre.2
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.2.0-pre.1
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    compatible:
    - 5.1.1
    - 5.1.2
    - 5.1.3
    recommended: 5.1.3
    deprecated: []
  dependencies:
  - name: com.autodesk.fbx
    version: 5.1.1
  - name: com.unity.timeline
    version: 1.7.1
  resolvedDependencies:
  - name: com.autodesk.fbx
    version: 5.1.1
  - name: com.unity.timeline
    version: 1.8.8
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - fbx
  - animation
  - modeling
  - maya
  - max
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638732425792020000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.formats.fbx@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.formats.fbx.git
    revision: 1dc43fa4b17f9e2d73204dbdd2463d060ddeb36c
    path: 
  unityLifecycle:
    version: 5.1.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an indentation issue in FBX Exporter
    Project Settings that was preventing straightforward toggling of checkboxes."}'
  assetStore:
    productId: 
  fingerprint: db39de05b0dbacefb3aa3035d97d497649e2e711
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.1
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.terrain-tools@5.2.1
  testable: 0
  isDirectDependency: 0
  version: 5.2.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.terrain-tools@332ce82b363c
  assetPath: Packages/com.unity.terrain-tools
  name: com.unity.terrain-tools
  displayName: Terrain Tools
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The Terrain Tools package adds additional Terrain sculpting brushes
    and tools to Unity. This add-on toolset is suitable if you require more control
    over the appearance of your Terrain, and want to streamline Terrain workflows.
    It\u2019s also useful when you want to make more complex-looking Terrain, or
    author Terrain Texture data in external digital content creation tools."
  errors: []
  versions:
    all:
    - 1.1.2-preview
    - 1.1.4-preview
    - 2.0.0-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 3.0.0-preview
    - 3.0.1-preview
    - 3.0.2-preview.2
    - 3.0.2-preview.3
    - 4.0.0-pre.2
    - 4.0.0
    - 4.0.3
    - 4.0.5
    - 5.0.0-pre.2
    - 5.0.0-pre.3
    - 5.0.0-pre.4
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.0.4
    - 5.0.5
    - 5.0.6
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.2.1
    - 5.3.0
    compatible:
    - 5.2.1
    - 5.3.0
    recommended: 5.2.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.terrainphysics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.terrainphysics
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords:
  - Terrain
  - Brush
  - Toolbox
  - Mask
  - Filter
  - Noise
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638775662734540000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.terrain-tools@5.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/TerrainTools.git
    revision: 523252ebeb35751a9a1059dce1fff9dda2e9ae2f
    path: 
  unityLifecycle:
    version: 5.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed:\n- Fixed issue where new tests were not
    set to be internal, causing internal checks to fail.\n- Fixed incorrect line
    endings on some shaders that caused internal checks to fail."}'
  assetStore:
    productId: 
  fingerprint: 332ce82b363c88ee8d8eb8688cab207a46abb6dc
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.2.1
    minimumUnityVersion: 2023.1.0a25
- packageId: com.unity.mobile.android-logcat@1.4.5
  testable: 0
  isDirectDependency: 0
  version: 1.4.5
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.mobile.android-logcat@0ddcd2133dc3
  assetPath: Packages/com.unity.mobile.android-logcat
  name: com.unity.mobile.android-logcat
  displayName: Android Logcat
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Android Logcat package provides support for:\n - Android log messages\n
    - Android application memory statistics\n - Input injection\n - Android Screen
    Capture\n - Android Screen Recorder\n - Stacktrace Utility\n\nClick the 'View
    documentation' link above for more information.\n\nThe window can be accessed
    in Unity Editor via 'Window > Analysis > Android Logcat', or simply by pressing
    'Alt+6' on Windows or 'Option+6' on macOS. \n\nMake sure to have Android module
    loaded and switch to Android build target in 'Build Settings' window if the menu
    doesn't exist."
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.2.7-preview
    - 1.0.0
    - 1.0.1-preview
    - 1.0.3-preview
    - 1.1.0
    - 1.1.1
    - 1.2.0-preview
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.2
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    compatible:
    - 1.4.5
    recommended: 1.4.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - Mobile
  - Android
  - Logcat
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638801672940570000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mobile.android-logcat@1.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.mobile.logcat.git
    revision: 3699423c243d638fcec959c0a8fe07aee8a6c46c
    path: 
  unityLifecycle:
    version: 1.4.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes & Improvements\n - Fix \"llvm-nm.exe: error:
    : unknown argument ''-e''\" when resolving stacktraces on Windows. Was happening
    with Unity version 6000.0.44f1"}'
  assetStore:
    productId: 
  fingerprint: 0ddcd2133dc3642d39aae1db273b1a54bed03644
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.4.5
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.adaptiveperformance@5.1.4
  testable: 0
  isDirectDependency: 0
  version: 5.1.4
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.adaptiveperformance@f25c04dfc305
  assetPath: Packages/com.unity.adaptiveperformance
  name: com.unity.adaptiveperformance
  displayName: Adaptive Performance
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The Adaptive Performance package provides an API to get feedback
    about the thermal and power state of mobile devices, enabling applications to
    make performance-relevant adaptions at runtime.


    For instruction on how
    to use samples, please read the documentation.


    By installing this package,
    you agree to the terms and conditions of the Unity End User License Agreement
    found under "View licenses" above.'
  errors: []
  versions:
    all:
    - 0.2.0-preview.1
    - 1.0.0
    - 1.0.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.0-preview.5
    - 1.1.0-preview.7
    - 1.1.0
    - 1.1.6
    - 1.1.9
    - 1.1.10
    - 1.2.0
    - 1.2.2
    - 1.2.3
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0-preview.4
    - 2.0.0-preview.7
    - 2.0.0-preview.8
    - 2.0.0-preview.9
    - 2.0.0-preview.10
    - 2.0.0-preview.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.2.1
    - 2.2.3
    - 2.2.4
    - 3.0.0-pre.2
    - 3.0.0
    - 3.0.2
    - 3.0.3
    - 4.0.0-pre.1
    - 4.0.0
    - 4.0.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0-pre.3
    - 5.0.0-pre.4
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    - 5.1.4
    compatible:
    - 5.1.4
    recommended: 5.1.4
    deprecated: []
  dependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.modules.subsystems
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - mobile
  - adaptive
  - performance
  - utility
  - utilities
  - core
  - samsung
  - ap
  - provider
  - subsystem
  - indexer
  - scaler
  - vrr
  - boost
  - profiles
  - cluster
  - clusterinfo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638818243367650000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.adaptiveperformance@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.adaptiveperformance.git
    revision: 9cf9dcd7f622b8243336e2e3574de8a189b2af15
    path: 
  unityLifecycle:
    version: 5.1.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed Environment Documentation to ensure
    the samples reference the environment sample."}'
  assetStore:
    productId: 
  fingerprint: f25c04dfc305fbce5593f78b1af66dfb77b75cb1
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.4
    minimumUnityVersion: 2022.3.0f1
- packageId: com.unity.mobile.notifications@2.4.1
  testable: 0
  isDirectDependency: 0
  version: 2.4.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.mobile.notifications@ec0aacc640b7
  assetPath: Packages/com.unity.mobile.notifications
  name: com.unity.mobile.notifications
  displayName: Mobile Notifications
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Mobile Notifications package adds support for scheduling local repeatable
    or one-time notifications on iOS and Android.


    On iOS receiving of push
    notifications is also supported.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.7
    - 1.0.0-preview.8
    - 1.0.0-preview.9
    - 1.0.0-preview.10
    - 1.0.0-preview.13
    - 1.0.0-preview.17
    - 1.0.0-preview.21
    - 1.0.0-preview.23
    - 1.0.2
    - 1.0.3-preview
    - 1.0.3-preview.3
    - 1.0.3-preview.4
    - 1.0.3-preview.5
    - 1.0.3-preview.6
    - 1.0.3
    - 1.0.4-preview.1
    - 1.0.4-preview.2
    - 1.0.4-preview.3
    - 1.0.4-preview.4
    - 1.0.4-preview.5
    - 1.0.4-preview.6
    - 1.0.4-preview.9
    - 1.1.0-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.3.0
    - 1.3.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.2.0
    - 2.2.1
    - 2.2.2
    - 2.3.0
    - 2.3.1
    - 2.3.2
    - 2.4.0
    - 2.4.1
    compatible:
    - 2.4.0
    - 2.4.1
    recommended: 2.4.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.androidjni
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.androidjni
    version: 1.0.0
  keywords:
  - Android
  - iOS
  - Notifications
  - Mobile
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638851666062100000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mobile.notifications@2.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.mobile.notifications.git
    revision: 6cde88c42d5e47f1810284aded41399c52273425
    path: 
  unityLifecycle:
    version: 2.4.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes:\n- [Android] Add missing proguard rule for
    NotificationChannelWrapper."}'
  assetStore:
    productId: 
  fingerprint: ec0aacc640b7d2e724393686de37e1602e8e2343
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.4.0
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 1
  version: 2.0.23
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.23
    minimumUnityVersion: 2019.4.25f1
- packageId: com.unity.ide.rider@3.0.36
  testable: 0
  isDirectDependency: 1
  version: 3.0.36
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638799562419060000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: b5315083ab3861d21f6ab2ed0d9514daf04bf208
    path: 
  unityLifecycle:
    version: 3.0.36
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- fix RIDER-124592 Avoid affecting \"Strip Engine Code\"
    while IL2CPP debug enabled"}'
  assetStore:
    productId: 
  fingerprint: 4d374c7eb6db6907c7e6925e3086c3c73f926e13
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.36
    minimumUnityVersion: 2019.4.6f1
- packageId: com.unity.editorcoroutines@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.editorcoroutines@7d48783e7b8c
  assetPath: Packages/com.unity.editorcoroutines
  name: com.unity.editorcoroutines
  displayName: Editor Coroutines
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'The editor coroutines package allows developers to start constructs
    similar to Unity''s monobehaviour based coroutines within the editor using abitrary
    objects. '
  errors: []
  versions:
    all:
    - 0.0.1-preview.3
    - 0.0.1-preview.4
    - 0.0.1-preview.5
    - 0.0.2-preview.1
    - 0.1.0-preview.1
    - 0.1.0-preview.2
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - coroutine
  - coroutines
  - editor
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637232611380000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.editorcoroutines.git
    revision: f67fc9992bbc7a553b17375de53a8b2db136528e
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2018.1.0a1
- packageId: com.unity.performance.profile-analyzer@1.2.3
  testable: 0
  isDirectDependency: 0
  version: 1.2.3
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.performance.profile-analyzer@a68e7bc84997
  assetPath: Packages/com.unity.performance.profile-analyzer
  name: com.unity.performance.profile-analyzer
  displayName: Profile Analyzer
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The Profile Analyzer tool supports the standard Unity Profiler. You
    can use it to analyze multiple frames and multiple data sets of the CPU data
    in the Profiler.\n\nMain features: \n\u25AA Multi-frame analysis of a single
    set of Profiler CPU data \n\u25AA Comparison of two multi-frame profile scans
    \n\n"
  errors: []
  versions:
    all:
    - 0.4.0-preview.3
    - 0.4.0-preview.5
    - 0.4.0-preview.6
    - 0.5.0-preview.1
    - 0.6.0-preview.1
    - 0.7.0-preview.4
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.1.0-pre.2
    - 1.1.0
    - 1.1.1
    - 1.2.2
    - 1.2.3
    compatible:
    - 1.2.3
    recommended: 1.2.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638699545898200000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git
    revision: 835e61d94bd201d0dea5763dff75e86b6b61de29
    path: 
  unityLifecycle:
    version: 1.2.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n* Fixed minimum supported version in documentation.\n*
    Fixed PROFB-199; Unchecking ''Hide Removed Markers'' doesn''t work."}'
  assetStore:
    productId: 
  fingerprint: a68e7bc849973d943853204178d08a2bc7656ffe
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.3
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.testtools.codecoverage@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39
  assetPath: Packages/com.unity.testtools.codecoverage
  name: com.unity.testtools.codecoverage
  displayName: Code Coverage
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use this package to export code coverage data and reports from your
    automated tests. Additionally, the Code Coverage package offers a Coverage Recording
    feature which allows capturing coverage data on demand, for manual testing or
    when there are no automated tests in the project.
  errors: []
  versions:
    all:
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.4.2-preview
    - 0.4.3-preview
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.2.0-exp.1
    - 1.2.0-exp.2
    - 1.2.0-exp.3
    - 1.2.0-exp.4
    - 1.2.0-exp.5
    - 1.2.0-exp.6
    - 1.2.0-exp.7
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    compatible:
    - 1.2.6
    recommended: 1.2.6
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.0.16
  - name: com.unity.settings-manager
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - test
  - coverage
  - testing
  - opencover
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575857900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.testtools.codecoverage.git
    revision: 959db8ff97eedf9b211ad5cf320b87ac01f1e90f
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes\n- Documentation: Fixed formatting in [Using
    Code Coverage in batchmode](https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/CoverageBatchmode.html)
    page (case [COV-40](https://issuetracker.unity3d.com/issues/docs-formatting-in-using-code-coverage-in-batchmode-docs-page-is-incorrect)).\n-
    Removed the references to the deprecated FindObjectOfType method in the *Asteroids
    sample project* (case [COV-42](https://issuetracker.unity3d.com/issues/sample-project-is-using-obsolete-findobjectoftype-method-which-causes-multiple-warnings-in-console-when-it-is-imported)).\n-
    Added missing logs for the ReportGenerator (case [COV-46](https://issuetracker.unity3d.com/issues/code-coverage-package-does-not-report-some-of-the-internal-reportgenerator-errors))."}'
  assetStore:
    productId: 
  fingerprint: 205a02cbcb39584f20b51c49b853047aceb3a3a7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.6
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.animation.rigging@1.3.0
  testable: 0
  isDirectDependency: 0
  version: 1.3.0
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.animation.rigging@68167b505d2b
  assetPath: Packages/com.unity.animation.rigging
  name: com.unity.animation.rigging
  displayName: Animation Rigging
  author:
    name: 
    email: 
    url: 
  category: Animation
  type: asset
  description: Animation Rigging toolkit using Unity's Animation C# Jobs
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.1.4-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.2.5-preview
    - 0.2.6-preview
    - 0.2.7-preview
    - 0.3.2-preview
    - 0.3.3-preview
    - 0.3.4-preview
    - 1.0.2
    - 1.0.3
    - 1.1.0
    - 1.1.1
    - 1.2.0
    - 1.2.1
    - 1.3.0
    compatible:
    - 1.3.0
    recommended: 1.3.0
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.4.1
  - name: com.unity.test-framework
    version: 1.1.24
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - Animation
  - Rigging
  - Constraints
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638110143470000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.animation.rigging@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.animation.rigging.git
    revision: 56e7b7c84bac20a2a5885468b6849c14b3ef49f4
    path: 
  unityLifecycle:
    version: 1.3.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Added the `TransformHandle`''s function `GetLocalToParentMatrix`
    to get the matrix of an animation stream transform in local space.\n- Added the
    `TransformHandle`''s function `GetLocalToWorldMatrix` to get the matrix of an
    animation stream transform in world space.\n- Fixed handling negative scale in
    the `MultiAimConstraintJob` (case 1366549).\n- Fixed transforms in animator hierarchy,
    but not children of avatar root not resolving properly (case 1373387).\n- Fixed
    MultiAimConstraint evaluation with a world up axis (UM-1936).\n- Fixed crash
    when calling `RigBuilder.Build` by preventing rebuilding the PlayableGraph when
    in a preview context (case UUM-8599).\n- Fixed an issue where a misplaced `BoneHandles.shader`
    shader would cause the Scene View''s Orientation Overlay to no longer render
    (case UUM-20874)."}'
  assetStore:
    productId: 
  fingerprint: 68167b505d2b550a35f5d5b6daef6193de3c7460
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.0
    minimumUnityVersion: 2022.2.0a9
- packageId: com.unity.timeline@1.8.8
  testable: 0
  isDirectDependency: 0
  version: 1.8.8
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.timeline@fa3a0bab2b90
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    compatible:
    - 1.8.7
    - 1.8.8
    recommended: 1.8.8
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638739338167810000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: b01f8fc6766a468476830dec57fdf694f16104e6
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n- Fixed bug where the first property,
    when it is a collection of objects, of a TrackAsset would not be properly displayed
    in the inspector.\n- TimelineAsset.EditorSettings.SetStandardFrameRate would
    incorrectly throw an ArgumentException if given a valid StandardFrameRates, this
    has been corrected.\n- Clip blends will now be computed when using the API to
    add clips to tracks - IN-66759\n- Improved performance when evaluating Timeline
    Playables with a large number of clip based Animation Tracks (TB-259)\n- Updated
    the Gameplay Sequence sample to show materials when using the Universal and HD
    Render Pipelines."}'
  assetStore:
    productId: 
  fingerprint: fa3a0bab2b909389f8e6c20d5ff275c9e15ae0a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.cinemachine@3.1.4
  testable: 0
  isDirectDependency: 0
  version: 3.1.4
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.cinemachine@b66fdb7cd1f2
  assetPath: Packages/com.unity.cinemachine
  name: com.unity.cinemachine
  displayName: Cinemachine
  author:
    name: 
    email: 
    url: 
  category: cinematography
  type: 
  description: "Smart camera tools for passionate creators. \n\nCinemachine 3 is
    a newer and better version of Cinemachine, but upgrading an existing project
    from 2.X will likely require some effort.  If you're considering upgrading an
    older project, please see our upgrade guide in the user manual."
  errors: []
  versions:
    all:
    - 2.1.11-beta.1
    - 2.1.12
    - 2.1.13
    - 2.2.0
    - 2.2.7
    - 2.2.8
    - 2.2.9
    - 2.2.10-preview.3
    - 2.2.10-preview.4
    - 2.3.1
    - 2.3.3
    - 2.3.4
    - 2.3.5-preview.3
    - 2.4.0-preview.3
    - 2.4.0-preview.4
    - 2.4.0-preview.6
    - 2.4.0-preview.7
    - 2.4.0-preview.8
    - 2.4.0-preview.9
    - 2.4.0-preview.10
    - 2.4.0
    - 2.5.0
    - 2.6.0-preview.2
    - 2.6.0-preview.3
    - 2.6.0-preview.5
    - 2.6.0-preview.8
    - 2.6.0
    - 2.6.1-preview.6
    - 2.6.1
    - 2.6.2-preview.1
    - 2.6.2
    - 2.6.3-preview.2
    - 2.6.3
    - 2.6.4
    - 2.6.5
    - 2.6.9
    - 2.6.10
    - 2.6.11
    - 2.6.14
    - 2.6.15
    - 2.6.17
    - 2.7.1
    - 2.7.2
    - 2.7.3
    - 2.7.4
    - 2.7.5
    - 2.7.8
    - 2.7.9
    - 2.8.0-exp.1
    - 2.8.0-exp.2
    - 2.8.0-pre.1
    - 2.8.0
    - 2.8.1
    - 2.8.2
    - 2.8.3
    - 2.8.4
    - 2.8.6
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    compatible:
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    recommended: 3.1.4
    deprecated: []
  dependencies:
  - name: com.unity.splines
    version: 2.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.splines
    version: 2.8.1
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - camera
  - follow
  - rig
  - fps
  - cinematography
  - aim
  - orbit
  - cutscene
  - cinematic
  - collision
  - freelook
  - cinemachine
  - compose
  - composition
  - dolly
  - track
  - clearshot
  - noise
  - framing
  - handheld
  - lens
  - impulse
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638851617780470000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.cinemachine@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
    revision: b5d928f5923583585b11d5c17531ff59c8c971c9
    path: 
  unityLifecycle:
    version: 2.10.3
    nextVersion: 
    recommendedVersion: 3.1.0
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bugfixes\n- Deoccluder did not always properly
    reset its state.\n- Deoccluder and Decollider were introducing spurious damping
    when the FreeLook orbit size changed.\n- Mac only: Some dropdowns and popups
    in Cinemachine inspectors did not work consistently.\n- Regression fix: Confiner2D
    was not always confining when a camera was newly activated.\n- RotationComposer
    and PositionComposer no longer damp in response to composition changes from the
    FreeLookModifier.\n- Fixed sample asset selection at import depending on current
    render pipeline and input configuration.  You need to re-import the samples to
    fix any existing issues.\n- Confiner2D was behaving inconsistently for large
    sized bounding boxes due to precision issues.\n- StateDrivenCamera inspector
    was not populating the states in the instruction list correctly.\n- Fixed a number
    of issues causing ForceCameraPosition to not behave as expected.\n- Maximizing
    an inspector which contained custom blends would generate console errors and
    cause the inspector to be unresponsive.\n\n### Changed\n- Cinemachine Shot Editor
    for Timeline no longer displays improper UX to create cameras when editing a
    prefab.\n- The game-view composer guides dynamically reflect the current composition
    when a FreeLookModifier is changing it.\n- When a FreeLookModifier is enabled
    for composition, the game-view composer guides are no longer draggable.\n \n###
    Added\n- Added Easing setting to FreeLookModifier, to smooth the transition between
    top and bottom FreeLook hemispheres.\n- Added an overload for `CinemachineBrain.ManualUpdate`
    which takes a custom frame count and deltaTime, allowing more customized control
    over the game loop.\n- Added `CINEMACHINE_TRANSPARENT_POST_PROCESSING_BLENDS`
    scripting define to tune the PostProcessing blend behaviour.\n- Added Signal
    Combination Mode to Impulse Listener, to allow combining multiple impulse signals
    in different ways."}'
  assetStore:
    productId: 
  fingerprint: b66fdb7cd1f2f796860f420c11c2033b2262c986
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.10.3
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.searcher@4.9.3
  testable: 0
  isDirectDependency: 0
  version: 4.9.3
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.searcher@1e17ce91558d
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731478077990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 6fad693b6604ae7175b59ebb4990d9a0b6c1d012
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed a bug where spaces are removed when highlighted.\n-
    Changed VisualSplitter to twoPaneSplitView and updated indentation"}'
  assetStore:
    productId: 
  fingerprint: 1e17ce91558d1d9127554adc03d275f39a7466a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
- packageId: com.unity.burst@1.8.21
  testable: 0
  isDirectDependency: 0
  version: 1.8.21
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.burst@59eb6f11d242
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    compatible:
    - 1.8.21
    - 1.8.22
    recommended: 1.8.22
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638797336625580000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 941bff0925baa46012a2eff522313c170bf36c7a
    path: 
  unityLifecycle:
    version: 1.8.21
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- update EmbeddedLinux argument syntax to support newer
    clang versions\n\n### Added\n- Added LLVM 19 Support\n- Added stack protector
    AOT settings options.\n\n### Removed\n\n### Fixed\n- Fixed hashing error that
    could occur when an assembly contained a type reference with a \"module\" resolution
    scope\n- Fixed internal compiler error when using a `FixedStringNBytes` value
    in an interpolated string\n- Fixed a crash caused by assigning `null` to a `Span`\n-
    Fixed \"Unable to resolve the method\" error that occurred when two or more methods
    had the same name, one of the methods contained a generic parameter, and the
    generic parameter type had two or more generic arguments\n- Fixed handling of
    `stackalloc`ed arrays with initial values for newer versions of Visual Studio\n-
    When building for Android with the Mono scripting backend, arm64-v8a symbols
    could be incorrectly included in the output, even though Mono for Android only
    supports armv7. This is now fixed.\n- Fixed compiler crash that could happen
    when using an interface method that had a default implementation defined in another
    interface\n- Fixed cropping of tooltips in the inspector.\n- Fixed rare \"Unhandled
    exception. System.InvalidCastException: Unable to cast object of type ''System.IO.MemoryStream''
    to type ''System.Text.StringBuilder''\" error that could occur during Burst compilation\n-
    Fixed a `BC1054` error that could occur if a struct with a pointer-to-generic-parameter-typed
    field was used as a generic argument to an interface type\n- Fixed compiler crash
    when trying to use the `Span<T>(T[])` or `Span<T>(T[],int,int)` constructors\n\n###
    Changed\n- EmbeddedLinux SSE4 default\n\n### Known Issues\n- With LLVM 19, Burst''s
    alias analysis is more conservative than it needs to be which may result in performance
    reductions in Burst compiled code. This is will be addressed in the next Burst
    release. Note that at this time, Burst by default uses LLVM 18."}'
  assetStore:
    productId: 
  fingerprint: 59eb6f11d2422f95682320d9daa3e79fdb076744
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.21
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.collections@2.5.1
  testable: 0
  isDirectDependency: 0
  version: 2.5.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.collections@56bff8827a7e
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    compatible:
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638621282722800000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 5b0dea6b455f5df005c19fa984ddfa237d6cd707
    path: 
  unityLifecycle:
    version: 2.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated Burst dependency to version
    1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated
    entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor
    to fail, blocking compilation, but no more."}'
  assetStore:
    productId: 
  fingerprint: 56bff8827a7ef6d44fcee4f36e558a74da89c1a0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.1
    minimumUnityVersion: 2022.3.11f1
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.rendering.light-transport@ec31b4120e30
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: ec31b4120e30d44c7f702fa7bfa50d70b562cd4a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
- packageId: com.autodesk.fbx@5.1.1
  testable: 0
  isDirectDependency: 0
  version: 5.1.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.autodesk.fbx@5797ff6b31c7
  assetPath: Packages/com.autodesk.fbx
  name: com.autodesk.fbx
  displayName: Autodesk FBX SDK for Unity
  author:
    name: Unity
    email: 
    url: 
  category: 
  type: assets
  description: "This package provides Unity C# bindings to the Autodesk\xAE FBX\xAE
    SDK."
  errors: []
  versions:
    all:
    - 2.0.0-preview
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.6
    - 2.0.0-preview.7
    - 2.0.1-preview.1
    - 3.0.0-preview.1
    - 3.0.1-preview.1
    - 3.1.0-preview.1
    - 3.1.0-preview.2
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 4.0.1
    - 4.1.0-pre.1
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.2.0-pre.1
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0
    - 5.1.0-pre.1
    - 5.1.0
    - 5.1.1
    compatible:
    - 5.1.1
    recommended: 5.1.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - fbx
  - animation
  - modeling
  - maya
  - max
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638464332400000000
  documentationUrl: https://docs.unity3d.com/Packages/com.autodesk.fbx@5.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.autodesk.fbx.git
    revision: dccfc2a79ac7abfe7bd6f91f713f990f2fedd141
    path: 
  unityLifecycle:
    version: 5.1.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Update FBX SDK to 2020.3.4.\n* Add support for Windows
    ARM64."}'
  assetStore:
    productId: 
  fingerprint: 5797ff6b31c7c66189bffe1adc9cbf0ad5a9cbbf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.1.1
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.settings-manager@2.1.0
  testable: 0
  isDirectDependency: 0
  version: 2.1.0
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.settings-manager@41738c275190
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.1.0
    recommended: 2.1.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792046006940000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.settings-manager@2.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 6eb5ed85eaf3276cc09f0cc4fc3311c72f0fc196
    path: 
  unityLifecycle:
    version: 2.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changes\n\n- Enable Auto Reference on main assembly.\n\n###
    Bug Fixes\n\n- [case: PBLD-56] Added tooltip to \"Options\" gear icon to specify
    that context menu items are relevant only to the selected category."}'
  assetStore:
    productId: 
  fingerprint: 41738c27519039c335849eb78949382f4d7a3544
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.1.0
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.profiling.core@1.0.2
  testable: 0
  isDirectDependency: 0
  version: 1.0.2
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.profiling.core@aac7b93912bc
  assetPath: Packages/com.unity.profiling.core
  name: com.unity.profiling.core
  displayName: Unity Profiling Core API
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: The Unity Profiling Core package provides an API for code instrumentation
    markup, and for profiling statistic collection.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 0.2.0-preview.1
    - 0.2.1-preview.1
    - 1.0.0-pre.1
    - 1.0.0
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - profiler
  - profiling
  - api
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637828752310000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/profiler.git
    revision: 2189ba14439d76a4083f59fae87163b4bdfd49c2
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: aac7b93912bc5df5fe06b04ff1b758493cdc2346
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 2020.1.0a1
- packageId: com.unity.splines@2.8.1
  testable: 0
  isDirectDependency: 0
  version: 2.8.1
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.splines@b909627b5095
  assetPath: Packages/com.unity.splines
  name: com.unity.splines
  displayName: Splines
  author:
    name: 
    email: 
    url: 
  category: Tool
  type: 
  description: Work with curves and paths. Use the Splines package to generate objects
    and behaviors along paths, create trajectories, and draw shapes.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 1.0.0-pre.5
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0
    - 1.0.1
    - 2.0.0-pre.2
    - 2.0.0-pre.4
    - 2.0.0
    - 2.1.0
    - 2.2.0
    - 2.2.1
    - 2.3.0
    - 2.4.0
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.6.1
    - 2.7.1
    - 2.7.2
    - 2.8.0
    - 2.8.1
    compatible:
    - 2.8.1
    recommended: 2.8.1
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 1.0.3
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - spline
  - curve
  - path
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638822348021470000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.splines@2.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.splines.git
    revision: ae2c33950924058f2912683d16e02268813ce44a
    path: 
  unityLifecycle:
    version: 2.8.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bug Fixes\n- [SPLB-345] Fixed a bug which was causing
    null reference exceptions during shutdown in IL2CPP builds.\n- [SPLB-337] Fixed
    a bug where `JoinSplinesOnKnots` would throw a null reference exception when
    one of the splines was linked with another spline.\n- [SPLB-341] Fixed a bug
    where changing the tangent mode on a knot on a prefab would not persist when
    entering play mode.\n\n### Changed\n- Internal code cleanup to align with release
    standards."}'
  assetStore:
    productId: 
  fingerprint: b909627b5095061d48761597bbb8384d6f04e510
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.8.1
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.nuget.mono-cecil@1.11.4
  testable: 0
  isDirectDependency: 0
  version: 1.11.4
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suite using the existing community tooling.

    To avoid assembly clashes, please
    use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.4
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637852971930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: d0133ce672d724694b56bfd20672acf6f8737fec
    path: 
  unityLifecycle:
    version: 1.11.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.4
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.test-framework.performance@3.1.0
  testable: 0
  isDirectDependency: 0
  version: 3.1.0
  source: 1
  resolvedPath: C:\squadmateai\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.1.0
    recommended: 3.1.0
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792643567670000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 6126c0ee357019ed762100ffeca520029274e869
    path: 
  unityLifecycle:
    version: 3.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added an optional command-line argument
    \"perfTestResults\" to control the target location of performance test run results
    file.\n### Fixed\n- Warmup cycles no longer record GC measurements.\n- Setup
    and Cleanup cycles no longer contribute to GC measurements."}'
  assetStore:
    productId: 
  fingerprint: 92d1d09a72ed696fa23fd76c675b29d211664b50
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.1.0
    minimumUnityVersion: 2020.3.0a1
m_BuiltInPackagesHash: 2aed1efaaf8c3b1deb500134fc31ba2f79781a84
