using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Simple test class for SquadMateAgent compilation fixes
    /// </summary>
    public class SimpleSquadMateAgentTests : MonoBehaviour
    {
        [ContextMenu("Test SquadMateAgent.hasWeapon Property")]
        public void TestSquadMateAgentHasWeaponProperty()
        {
            Debug.Log("🧪 Testing SquadMateAgent.hasWeapon property...");
            
            try
            {
                // Create test agent
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                // Test 1: Initial state
                bool initialState = agent.hasWeapon;
                Debug.Log($"  Initial hasWeapon state: {initialState}");
                
                // Test 2: Setting weapon
                agent.hasWeapon = true;
                bool weaponState = agent.hasWeapon;
                Debug.Log($"  After setting true: {weaponState}");
                
                // Test 3: Removing weapon
                agent.hasWeapon = false;
                bool noWeaponState = agent.hasWeapon;
                Debug.Log($"  After setting false: {noWeaponState}");
                
                // Test 4: Multiple operations
                agent.hasWeapon = true;
                bool canAttack = agent.hasWeapon && true;
                string status = agent.hasWeapon ? "Armed" : "Unarmed";
                Debug.Log($"  Can attack: {canAttack}, Status: {status}");
                
                // Clean up
                DestroyImmediate(testAgent);
                
                // Evaluate results
                bool testPassed = !initialState && weaponState && !noWeaponState && canAttack && status == "Armed";
                
                if (testPassed)
                {
                    Debug.Log("✅ SquadMateAgent.hasWeapon property test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ SquadMateAgent.hasWeapon property test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ SquadMateAgent.hasWeapon property test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test SquadMateAgent External Access")]
        public void TestSquadMateAgentExternalAccess()
        {
            Debug.Log("🧪 Testing SquadMateAgent external access...");
            
            try
            {
                // Create test agent
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                // Simulate external script access (like SupportAIMasterController)
                bool canRead = agent.hasWeapon; // Should be readable
                agent.hasWeapon = true; // Should be writable
                bool canWrite = agent.hasWeapon;
                
                // Test that other scripts can use it in conditions
                if (agent.hasWeapon)
                {
                    Debug.Log("  External script can use hasWeapon in conditions");
                }
                
                // Clean up
                DestroyImmediate(testAgent);
                
                bool testPassed = canWrite; // If we got here, external access works
                
                if (testPassed)
                {
                    Debug.Log("✅ SquadMateAgent external access test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ SquadMateAgent external access test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ SquadMateAgent external access test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test All SquadMateAgent Features")]
        public void TestAllSquadMateAgentFeatures()
        {
            TestSquadMateAgentHasWeaponProperty();
            TestSquadMateAgentExternalAccess();
        }
    }
}
