{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Editor.Tests.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Editor.Tests.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Editor.Tests.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Runtime.Tests.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Runtime.Tests.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/SquadMateAI.Runtime.Tests.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Analytics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Analytics.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Analytics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Csg.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.KdTree.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Stl.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.SysrootPackage.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.SysrootPackage.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.SysrootPackage.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.BuildTestAssets.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Polybrush.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sysroot.Linux_x86_64.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sysroot.Linux_x86_64.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sysroot.Linux_x86_64.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.DocCodeExamples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.MacBLAS.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.MacBLAS.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.MacBLAS.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.ONNX.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.ONNX.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.ONNX.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.iOSBLAS.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.iOSBLAS.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.iOSBLAS.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Sentis.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "C:/squadmateai/Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}], "LocalizeCompilerMessages": false}}