{"name": "SquadMateAI.Runtime.Tests", "rootNamespace": "", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "Unity.ML-Agents", "Assembly-CSharp"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}