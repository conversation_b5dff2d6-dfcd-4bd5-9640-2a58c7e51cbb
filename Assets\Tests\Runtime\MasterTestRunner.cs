using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Master test runner that executes all compilation fix tests
    /// </summary>
    public class MasterTestRunner : MonoBehaviour
    {
        [Header("Auto-run Settings")]
        public bool runTestsOnStart = false;
        
        [Header("Test Results")]
        public bool playerControllerTestsPassed = false;
        public bool squadMateAgentTestsPassed = false;
        public bool pubgScenariosTestsPassed = false;
        public bool integrationTestsPassed = false;
        public bool allTestsPassed = false;
        
        private void Start()
        {
            if (runTestsOnStart)
            {
                RunAllCompilationFixTests();
            }
        }
        
        [ContextMenu("Run All Compilation Fix Tests")]
        public void RunAllCompilationFixTests()
        {
            Debug.Log("🧪 === MASTER TEST RUNNER STARTED ===");
            Debug.Log("Running all compilation fix tests...");
            
            // Reset results
            playerControllerTestsPassed = false;
            squadMateAgentTestsPassed = false;
            pubgScenariosTestsPassed = false;
            integrationTestsPassed = false;
            allTestsPassed = false;
            
            // Run individual test suites
            playerControllerTestsPassed = RunPlayerControllerTests();
            squadMateAgentTestsPassed = RunSquadMateAgentTests();
            pubgScenariosTestsPassed = RunPUBGScenariosTests();
            integrationTestsPassed = RunIntegrationTests();
            
            // Calculate overall result
            allTestsPassed = playerControllerTestsPassed && 
                           squadMateAgentTestsPassed && 
                           pubgScenariosTestsPassed && 
                           integrationTestsPassed;
            
            // Final report
            Debug.Log("🧪 === TEST RESULTS SUMMARY ===");
            Debug.Log($"PlayerController Tests: {(playerControllerTestsPassed ? "✅ PASSED" : "❌ FAILED")}");
            Debug.Log($"SquadMateAgent Tests: {(squadMateAgentTestsPassed ? "✅ PASSED" : "❌ FAILED")}");
            Debug.Log($"PUBGScenarios Tests: {(pubgScenariosTestsPassed ? "✅ PASSED" : "❌ FAILED")}");
            Debug.Log($"Integration Tests: {(integrationTestsPassed ? "✅ PASSED" : "❌ FAILED")}");
            Debug.Log($"Overall Result: {(allTestsPassed ? "🎉 ALL TESTS PASSED! 🎉" : "❌ SOME TESTS FAILED")}");
            Debug.Log("🧪 === MASTER TEST RUNNER COMPLETED ===");
        }
        
        private bool RunPlayerControllerTests()
        {
            Debug.Log("Running PlayerController tests...");
            
            try
            {
                // Test PlayerController.isDowned property
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                bool initialState = !playerController.isDowned;
                playerController.GoDown();
                bool downedState = playerController.isDowned;
                playerController.Revive();
                bool revivedState = !playerController.isDowned;
                
                DestroyImmediate(testPlayer);
                
                bool result = initialState && downedState && revivedState;
                Debug.Log($"  PlayerController tests: {(result ? "PASSED" : "FAILED")}");
                return result;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  PlayerController tests FAILED: {e.Message}");
                return false;
            }
        }
        
        private bool RunSquadMateAgentTests()
        {
            Debug.Log("Running SquadMateAgent tests...");
            
            try
            {
                // Test SquadMateAgent.hasWeapon property
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                bool initialState = !agent.hasWeapon;
                agent.hasWeapon = true;
                bool weaponState = agent.hasWeapon;
                agent.hasWeapon = false;
                bool noWeaponState = !agent.hasWeapon;
                
                DestroyImmediate(testAgent);
                
                bool result = initialState && weaponState && noWeaponState;
                Debug.Log($"  SquadMateAgent tests: {(result ? "PASSED" : "FAILED")}");
                return result;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  SquadMateAgent tests FAILED: {e.Message}");
                return false;
            }
        }
        
        private bool RunPUBGScenariosTests()
        {
            Debug.Log("Running PUBGScenarios tests...");
            
            try
            {
                // Test PUBGTrainingScenarios.SwitchToNextScenario method
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                // Test that method is callable
                scenarios.SwitchToNextScenario();
                scenarios.SwitchToNextScenario();
                scenarios.SwitchToNextScenario();
                
                DestroyImmediate(testScenarios);
                
                bool result = true; // If we got here, the method is callable
                Debug.Log($"  PUBGScenarios tests: {(result ? "PASSED" : "FAILED")}");
                return result;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  PUBGScenarios tests FAILED: {e.Message}");
                return false;
            }
        }
        
        private bool RunIntegrationTests()
        {
            Debug.Log("Running Integration tests...");
            
            try
            {
                // Test all components working together
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                GameObject testAgent = new GameObject("TestAgent");
                SquadMateAgent agent = testAgent.AddComponent<SquadMateAgent>();
                testAgent.AddComponent<Rigidbody>();
                testAgent.AddComponent<CapsuleCollider>();
                
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                // Test cross-component communication
                playerController.GoDown();
                bool playerDownedCheck = playerController.isDowned;
                
                agent.hasWeapon = true;
                bool agentWeaponCheck = agent.hasWeapon;
                
                scenarios.SwitchToNextScenario();
                
                // Test Debug.Log works
                Debug.Log("  Debug.Log test - this confirms proper capitalization");
                
                DestroyImmediate(testPlayer);
                DestroyImmediate(testAgent);
                DestroyImmediate(testScenarios);
                
                bool result = playerDownedCheck && agentWeaponCheck;
                Debug.Log($"  Integration tests: {(result ? "PASSED" : "FAILED")}");
                return result;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"  Integration tests FAILED: {e.Message}");
                return false;
            }
        }
        
        [ContextMenu("Quick Verification")]
        public void QuickVerification()
        {
            Debug.Log("🔍 Quick verification of compilation fixes...");
            
            try
            {
                // Quick test of each fix
                GameObject player = new GameObject("QuickTestPlayer");
                PlayerController pc = player.AddComponent<PlayerController>();
                player.AddComponent<CharacterController>();
                player.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                bool playerTest = pc.isDowned == false; // Property accessible
                
                GameObject agent = new GameObject("QuickTestAgent");
                SquadMateAgent sma = agent.AddComponent<SquadMateAgent>();
                agent.AddComponent<Rigidbody>();
                agent.AddComponent<CapsuleCollider>();
                sma.hasWeapon = true; // Property writable
                bool agentTest = sma.hasWeapon == true; // Property readable
                
                GameObject scenarios = new GameObject("QuickTestScenarios");
                PUBGTrainingScenarios pts = scenarios.AddComponent<PUBGTrainingScenarios>();
                pts.SwitchToNextScenario(); // Method callable
                bool scenarioTest = true;
                
                Debug.Log("Quick test completed successfully"); // Debug.Log works
                bool debugTest = true;
                
                DestroyImmediate(player);
                DestroyImmediate(agent);
                DestroyImmediate(scenarios);
                
                bool allQuickTests = playerTest && agentTest && scenarioTest && debugTest;
                Debug.Log($"🔍 Quick verification: {(allQuickTests ? "✅ ALL FIXES WORKING" : "❌ SOME ISSUES FOUND")}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"🔍 Quick verification FAILED: {e.Message}");
            }
        }
    }
}
