{"name": "com.unity.ai.navigation", "displayName": "AI Navigation", "version": "2.0.8", "unity": "6000.0", "description": "The AI Navigation package contains high-level components that allow you to use navmeshes to incorporate navigation and pathfinding in your game. With this package installed you can build and use navmeshes at runtime and at edit time, create dynamic obstacles, and use links to allow specific actions (like jumping) as your characters navigate between navmeshes.", "dependencies": {"com.unity.modules.ai": "1.0.0"}, "keywords": ["carving", "mesh", "navigation", "nav<PERSON>h", "<PERSON><PERSON><PERSON><PERSON>", "navmesh agent", "navmesh link", "navmeshlink", "navmesh modifier", "navmeshmodifier", "navmesh modifier volume", "navmeshmodifiervolume", "navmesh obstacle", "navmesh surface", "navmeshsurface", "offmesh link", "off-mesh link", "pathfinding"], "samples": [{"displayName": "Build And Connect NavMesh Surfaces", "description": "A set of examples on how to build and use a dynamic navigation space made of NavMesh surfaces and segment-to-segment connections.", "path": "Samples~"}], "_upm": {"changelog": "### Fixed\n* Samples will now use appropriate material colors in projects using HDRP or URP. Install the **com.unity.shadergraph** package to show the materials correctly for built-in render pipeline projects."}, "upmCi": {"footprint": "34e1e7a4671713a2565d2b118d9e2fac85388927"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.ai.navigation@2.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.ai.navigation.git", "type": "git", "revision": "edeb8e889d1eedf4001fa41bdab7d69d01a62f24"}, "_fingerprint": "eb5635ad590d47cef2a5c920d9475cc222db3f67"}