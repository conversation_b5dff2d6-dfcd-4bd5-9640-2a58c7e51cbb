{"name": "SquadMateAI.Editor.Tests", "rootNamespace": "", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "Unity.ML-Agents"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}