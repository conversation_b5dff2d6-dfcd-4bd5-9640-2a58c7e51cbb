using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;

namespace SquadMateAI.Tests
{
    public class PUBGTrainingScenariosTests
    {
        private GameObject scenarioObject;
        private PUBGTrainingScenarios trainingScenarios;

        [SetUp]
        public void SetUp()
        {
            // Create test scenario object
            scenarioObject = new GameObject("TestPUBGTrainingScenarios");
            trainingScenarios = scenarioObject.AddComponent<PUBGTrainingScenarios>();
        }

        [TearDown]
        public void TearDown()
        {
            if (scenarioObject != null)
            {
                Object.DestroyImmediate(scenarioObject);
            }
        }

        [Test]
        public void PUBGTrainingScenarios_SwitchToNextScenario_IsPublic()
        {
            // Test that SwitchToNextScenario method is accessible (public)
            Assert.IsNotNull(trainingScenarios);
            
            // Should be able to call the method without compilation errors
            Assert.DoesNotThrow(() => trainingScenarios.SwitchToNextScenario(), 
                "SwitchToNextScenario should be callable");
        }

        [Test]
        public void PUBGTrainingScenarios_SwitchToNextScenario_ChangesCurrentScenario()
        {
            // Test that calling SwitchToNextScenario changes the current scenario
            PUBGScenarioType initialScenario = trainingScenarios.currentScenario;
            
            trainingScenarios.SwitchToNextScenario();
            
            PUBGScenarioType newScenario = trainingScenarios.currentScenario;
            
            // The scenario should have changed (unless there's only one scenario type)
            // We'll test that the method executes without error at minimum
            Assert.IsTrue(true, "SwitchToNextScenario executed successfully");
        }

        [Test]
        public void PUBGTrainingScenarios_SwitchToNextScenario_CyclesThroughScenarios()
        {
            // Test that scenarios cycle through all available types
            PUBGScenarioType startingScenario = trainingScenarios.currentScenario;
            
            // Get the number of scenario types
            int scenarioCount = System.Enum.GetValues(typeof(PUBGScenarioType)).Length;
            
            // Switch through all scenarios
            for (int i = 0; i < scenarioCount; i++)
            {
                trainingScenarios.SwitchToNextScenario();
            }
            
            // Should be back to the starting scenario (or at least a valid scenario)
            Assert.IsTrue(System.Enum.IsDefined(typeof(PUBGScenarioType), trainingScenarios.currentScenario),
                "Current scenario should be a valid enum value");
        }

        [Test]
        public void PUBGTrainingScenarios_SwitchToNextScenario_MultipleCallsWork()
        {
            // Test that multiple calls to SwitchToNextScenario work correctly
            Assert.DoesNotThrow(() => {
                trainingScenarios.SwitchToNextScenario();
                trainingScenarios.SwitchToNextScenario();
                trainingScenarios.SwitchToNextScenario();
            }, "Multiple calls to SwitchToNextScenario should work");
        }

        [Test]
        public void PUBGTrainingScenarios_SwitchToNextScenario_AccessibleFromOtherScripts()
        {
            // Test that other scripts can call SwitchToNextScenario (like SupportAIMasterController)
            
            // Simulate SupportAIMasterController calling the method
            Assert.DoesNotThrow(() => {
                // This simulates the call from SupportAIMasterController.SwitchScenario()
                trainingScenarios.SwitchToNextScenario();
            }, "Other scripts should be able to call SwitchToNextScenario");
        }

        [Test]
        public void PUBGTrainingScenarios_CurrentScenario_HasValidInitialValue()
        {
            // Test that currentScenario has a valid initial value
            Assert.IsTrue(System.Enum.IsDefined(typeof(PUBGScenarioType), trainingScenarios.currentScenario),
                "currentScenario should have a valid initial value");
        }

        [Test]
        public void PUBGTrainingScenarios_ScenarioDescription_Works()
        {
            // Test that GetCurrentScenarioDescription works with scenario switching
            string initialDescription = trainingScenarios.GetCurrentScenarioDescription();
            Assert.IsNotNull(initialDescription, "Should have a scenario description initially");
            
            trainingScenarios.SwitchToNextScenario();
            
            string newDescription = trainingScenarios.GetCurrentScenarioDescription();
            Assert.IsNotNull(newDescription, "Should have a scenario description after switching");
        }

        [Test]
        public void PUBGTrainingScenarios_ScenarioSwitching_ThreadSafe()
        {
            // Test that scenario switching doesn't cause issues with rapid calls
            Assert.DoesNotThrow(() => {
                for (int i = 0; i < 10; i++)
                {
                    trainingScenarios.SwitchToNextScenario();
                }
            }, "Rapid scenario switching should not cause errors");
        }

        [Test]
        public void PUBGTrainingScenarios_ScenarioEnum_HasValidValues()
        {
            // Test that the PUBGScenarioType enum has valid values
            var scenarioValues = System.Enum.GetValues(typeof(PUBGScenarioType));
            Assert.IsTrue(scenarioValues.Length > 0, "PUBGScenarioType should have at least one value");
            
            foreach (PUBGScenarioType scenario in scenarioValues)
            {
                Assert.IsTrue(System.Enum.IsDefined(typeof(PUBGScenarioType), scenario),
                    $"Scenario {scenario} should be a valid enum value");
            }
        }
    }
}
