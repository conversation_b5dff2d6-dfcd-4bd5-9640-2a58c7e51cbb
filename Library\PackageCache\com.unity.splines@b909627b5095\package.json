{"name": "com.unity.splines", "displayName": "Splines", "version": "2.8.1", "unity": "2022.3", "description": "Work with curves and paths. Use the Splines package to generate objects and behaviors along paths, create trajectories, and draw shapes.", "dependencies": {"com.unity.settings-manager": "1.0.3", "com.unity.mathematics": "1.2.1", "com.unity.modules.imgui": "1.0.0"}, "samples": [{"displayName": "Spline Examples (requires Shader Graph package)", "description": "Example code and scenes files showing how to use Splines.", "path": "Samples~/S<PERSON><PERSON>Examples"}], "keywords": ["spline", "curve", "path"], "category": "Tool", "relatedPackages": {"com.unity.splines.tests": "2.8.1"}, "_upm": {"changelog": "### Bug Fixes\n- [SPLB-345] Fixed a bug which was causing null reference exceptions during shutdown in IL2CPP builds.\n- [SPLB-337] Fixed a bug where `JoinSplinesOnKnots` would throw a null reference exception when one of the splines was linked with another spline.\n- [SPLB-341] Fixed a bug where changing the tangent mode on a knot on a prefab would not persist when entering play mode.\n\n### Changed\n- Internal code cleanup to align with release standards."}, "upmCi": {"footprint": "aad672cfeeabdb074ba394ba7b81c5120ba03901"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.splines@2.8/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.splines.git", "type": "git", "revision": "ae2c33950924058f2912683d16e02268813ce44a"}, "_fingerprint": "b909627b5095061d48761597bbb8384d6f04e510"}