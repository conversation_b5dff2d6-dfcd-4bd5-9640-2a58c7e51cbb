using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Simple test class for PlayerController compilation fixes
    /// </summary>
    public class SimplePlayerControllerTests : MonoBehaviour
    {
        [ContextMenu("Test PlayerController.isDowned Property")]
        public void TestPlayerControllerIsDownedProperty()
        {
            Debug.Log("🧪 Testing PlayerController.isDowned property...");
            
            try
            {
                // Create test player
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                // Test 1: Initial state
                bool initialState = playerController.isDowned;
                Debug.Log($"  Initial isDowned state: {initialState}");
                
                // Test 2: Going down
                playerController.GoDown();
                bool downedState = playerController.isDowned;
                Debug.Log($"  After GoDown(): {downedState}");
                
                // Test 3: Reviving
                playerController.Revive();
                bool revivedState = playerController.isDowned;
                Debug.Log($"  After Revive(): {revivedState}");
                
                // Test 4: Damage
                playerController.TakeDamage(playerController.maxHealth);
                bool damagedState = playerController.isDowned;
                Debug.Log($"  After fatal damage: {damagedState}");
                
                // Clean up
                DestroyImmediate(testPlayer);
                
                // Evaluate results
                bool testPassed = !initialState && downedState && !revivedState && damagedState;
                
                if (testPassed)
                {
                    Debug.Log("✅ PlayerController.isDowned property test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ PlayerController.isDowned property test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ PlayerController.isDowned property test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test PlayerController Event System")]
        public void TestPlayerControllerEventSystem()
        {
            Debug.Log("🧪 Testing PlayerController event system...");
            
            try
            {
                // Create test player
                GameObject testPlayer = new GameObject("TestPlayer");
                PlayerController playerController = testPlayer.AddComponent<PlayerController>();
                testPlayer.AddComponent<CharacterController>();
                testPlayer.AddComponent<ReviveSystem>(); // Manually add ReviveSystem
                
                bool eventFired = false;
                bool eventValue = false;
                
                // Subscribe to event
                playerController.OnPlayerDownedChanged += (downed) => {
                    eventFired = true;
                    eventValue = downed;
                };
                
                // Test event firing
                playerController.GoDown();
                bool downEventWorked = eventFired && eventValue;
                
                // Reset for revive test
                eventFired = false;
                eventValue = true;
                
                playerController.Revive();
                bool reviveEventWorked = eventFired && !eventValue;
                
                // Clean up
                DestroyImmediate(testPlayer);
                
                bool testPassed = downEventWorked && reviveEventWorked;
                
                if (testPassed)
                {
                    Debug.Log("✅ PlayerController event system test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ PlayerController event system test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ PlayerController event system test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test All PlayerController Features")]
        public void TestAllPlayerControllerFeatures()
        {
            TestPlayerControllerIsDownedProperty();
            TestPlayerControllerEventSystem();
        }
    }
}
