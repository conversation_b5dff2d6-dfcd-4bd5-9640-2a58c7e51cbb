using UnityEngine;
using UnityEngine.AI;
using Unity.AI.Navigation;
using System.Collections.Generic;

/// <summary>
/// PUBG Training Arena Scene Setup
/// Automatically creates complete training environment for Support AI
/// </summary>
public class PUBGTrainingArenaSetup : MonoBehaviour
{
    [Header("Arena Configuration")]
    public Vector3 arenaSize = new Vector3(50f, 1f, 50f);
    public Material groundMaterial;
    public Material coverMaterial;
    
    [Header("Prefab References")]
    public GameObject playerPrefab;
    public GameObject squadMateAgentPrefab;
    public GameObject enemyPrefab;
    public GameObject weaponPrefab;
    public GameObject medkitPrefab;
    public GameObject lootPrefab;
    public GameObject coverPrefab;
    
    [Header("Spawn Configuration")]
    public int enemyCount = 2;
    public int weaponCount = 3;
    public int medkitCount = 4;
    public int lootCount = 5;
    public int coverCount = 8;
    
    [Header("Auto Setup")]
    public bool setupOnStart = true;
    public bool createNavMesh = true;
    public bool enableTrainingScenarios = true;
    
    private List<GameObject> spawnedObjects = new List<GameObject>();
    private Transform playerTransform;
    private Transform agentTransform;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupTrainingArena();
        }
    }
    
    [ContextMenu("Setup Training Arena")]
    public void SetupTrainingArena()
    {
        Debug.Log("🏗️ Setting up PUBG Training Arena...");
        
        ClearExistingSetup();
        CreateTerrain();
        CreateSpawnPoints();
        SpawnPlayer();
        SpawnSquadMateAgent();
        SpawnEnemies();
        SpawnWeapons();
        SpawnMedkits();
        SpawnLoot();
        SpawnCover();
        
        if (createNavMesh)
        {
            SetupNavMesh();
        }
        
        if (enableTrainingScenarios)
        {
            SetupTrainingScenarios();
        }
        
        SetupLighting();
        SetupCamera();
        
        Debug.Log("✅ PUBG Training Arena setup complete!");
    }
    
    void ClearExistingSetup()
    {
        foreach (GameObject obj in spawnedObjects)
        {
            if (obj != null)
            {
                DestroyImmediate(obj);
            }
        }
        spawnedObjects.Clear();
    }
    
    void CreateTerrain()
    {
        // Create ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "TrainingGround";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(arenaSize.x / 10f, 1f, arenaSize.z / 10f);
        
        if (groundMaterial != null)
        {
            ground.GetComponent<Renderer>().material = groundMaterial;
        }
        
        // Mark as navigation static
        ground.isStatic = true;
        
        spawnedObjects.Add(ground);
        
        Debug.Log("🌍 Training ground created");
    }
    
    void CreateSpawnPoints()
    {
        // Create spawn point markers (invisible)
        GameObject spawnPoints = new GameObject("SpawnPoints");
        spawnedObjects.Add(spawnPoints);
        
        // Player spawn
        GameObject playerSpawn = new GameObject("PlayerSpawn");
        playerSpawn.transform.parent = spawnPoints.transform;
        playerSpawn.transform.position = new Vector3(-10f, 0.5f, -10f);
        
        // Agent spawn
        GameObject agentSpawn = new GameObject("AgentSpawn");
        agentSpawn.transform.parent = spawnPoints.transform;
        agentSpawn.transform.position = new Vector3(-8f, 0.5f, -8f);
        
        // Enemy spawns
        for (int i = 0; i < enemyCount; i++)
        {
            GameObject enemySpawn = new GameObject($"EnemySpawn_{i}");
            enemySpawn.transform.parent = spawnPoints.transform;
            
            float angle = (360f / enemyCount) * i;
            float distance = Random.Range(15f, 25f);
            Vector3 position = new Vector3(
                Mathf.Sin(angle * Mathf.Deg2Rad) * distance,
                0.5f,
                Mathf.Cos(angle * Mathf.Deg2Rad) * distance
            );
            enemySpawn.transform.position = position;
        }
    }
    
    void SpawnPlayer()
    {
        if (playerPrefab == null)
        {
            // Create basic player if no prefab provided
            GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "Player";
            player.transform.position = new Vector3(-10f, 1f, -10f);
            player.GetComponent<Renderer>().material.color = Color.blue;
            
            // Add basic components
            player.AddComponent<PlayerController>();
            player.AddComponent<HealthSystem>();
            
            spawnedObjects.Add(player);
            playerTransform = player.transform;
        }
        else
        {
            GameObject player = Instantiate(playerPrefab, new Vector3(-10f, 1f, -10f), Quaternion.identity);
            player.name = "Player";
            spawnedObjects.Add(player);
            playerTransform = player.transform;
        }
        
        Debug.Log("👤 Player spawned");
    }
    
    void SpawnSquadMateAgent()
    {
        if (squadMateAgentPrefab == null)
        {
            // Create basic agent if no prefab provided
            GameObject agent = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            agent.name = "SquadMateAgent";
            agent.transform.position = new Vector3(-8f, 1f, -8f);
            agent.GetComponent<Renderer>().material.color = Color.green;
            
            // Add AI components
            agent.AddComponent<NavMeshAgent>();
            agent.AddComponent<SquadMateAgent>();
            agent.AddComponent<SupportAIReward>();
            agent.AddComponent<WeaponSystem>();
            agent.AddComponent<HealthSystem>();
            
            // Configure SquadMateAgent
            SquadMateAgent agentScript = agent.GetComponent<SquadMateAgent>();
            agentScript.player = playerTransform;
            
            // Configure SupportAIReward
            SupportAIReward rewardScript = agent.GetComponent<SupportAIReward>();
            rewardScript.agent = agentScript;
            rewardScript.player = playerTransform;
            
            spawnedObjects.Add(agent);
            agentTransform = agent.transform;
        }
        else
        {
            GameObject agent = Instantiate(squadMateAgentPrefab, new Vector3(-8f, 1f, -8f), Quaternion.identity);
            agent.name = "SquadMateAgent";
            
            // Configure references
            SquadMateAgent agentScript = agent.GetComponent<SquadMateAgent>();
            if (agentScript != null)
            {
                agentScript.player = playerTransform;
            }
            
            SupportAIReward rewardScript = agent.GetComponent<SupportAIReward>();
            if (rewardScript != null)
            {
                rewardScript.agent = agentScript;
                rewardScript.player = playerTransform;
            }
            
            spawnedObjects.Add(agent);
            agentTransform = agent.transform;
        }
        
        Debug.Log("🤖 SquadMate AI Agent spawned with Support AI Reward system");
    }
    
    void SpawnEnemies()
    {
        for (int i = 0; i < enemyCount; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(15f, 25f);
            
            GameObject enemy;
            if (enemyPrefab != null)
            {
                enemy = Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                enemy.GetComponent<Renderer>().material.color = Color.red;
                enemy.AddComponent<EnemyAI>();
                enemy.AddComponent<HealthSystem>();
                enemy.AddComponent<NavMeshAgent>();
            }
            
            enemy.name = $"Enemy_{i}";
            enemy.layer = 8; // Enemy layer
            spawnedObjects.Add(enemy);
        }
        
        Debug.Log($"👹 {enemyCount} enemies spawned");
    }
    
    void SpawnWeapons()
    {
        for (int i = 0; i < weaponCount; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(5f, 20f);
            spawnPos.y = 0.5f;
            
            GameObject weapon;
            if (weaponPrefab != null)
            {
                weapon = Instantiate(weaponPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
                weapon.GetComponent<Renderer>().material.color = Color.yellow;
                weapon.AddComponent<LootItem>();
                weapon.GetComponent<LootItem>().lootType = LootItem.LootType.Weapon;
            }
            
            weapon.name = $"Weapon_{i}";
            weapon.layer = 9; // Loot layer
            spawnedObjects.Add(weapon);
        }
        
        Debug.Log($"🔫 {weaponCount} weapons spawned");
    }
    
    void SpawnMedkits()
    {
        for (int i = 0; i < medkitCount; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(3f, 15f);
            spawnPos.y = 0.5f;
            
            GameObject medkit;
            if (medkitPrefab != null)
            {
                medkit = Instantiate(medkitPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                medkit = GameObject.CreatePrimitive(PrimitiveType.Cube);
                medkit.GetComponent<Renderer>().material.color = Color.white;
                medkit.AddComponent<LootItem>();
                medkit.GetComponent<LootItem>().lootType = LootItem.LootType.Medkit;
            }
            
            medkit.name = $"Medkit_{i}";
            medkit.layer = 9; // Loot layer
            spawnedObjects.Add(medkit);
        }
        
        Debug.Log($"💊 {medkitCount} medkits spawned");
    }
    
    void SpawnLoot()
    {
        for (int i = 0; i < lootCount; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(2f, 18f);
            spawnPos.y = 0.5f;
            
            GameObject loot;
            if (lootPrefab != null)
            {
                loot = Instantiate(lootPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                loot = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                loot.GetComponent<Renderer>().material.color = Color.cyan;
                loot.AddComponent<LootItem>();
                loot.GetComponent<LootItem>().lootType = LootItem.LootType.Ammo;
            }
            
            loot.name = $"Loot_{i}";
            loot.layer = 9; // Loot layer
            spawnedObjects.Add(loot);
        }
        
        Debug.Log($"📦 {lootCount} loot items spawned");
    }
    
    void SpawnCover()
    {
        for (int i = 0; i < coverCount; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(8f, 22f);
            spawnPos.y = 1f;
            
            GameObject cover;
            if (coverPrefab != null)
            {
                cover = Instantiate(coverPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cover.transform.localScale = new Vector3(2f, 2f, 0.5f);
                cover.GetComponent<Renderer>().material.color = Color.gray;
                
                if (coverMaterial != null)
                {
                    cover.GetComponent<Renderer>().material = coverMaterial;
                }
            }
            
            cover.name = $"Cover_{i}";
            cover.isStatic = true;
            spawnedObjects.Add(cover);
        }
        
        Debug.Log($"🛡️ {coverCount} cover objects spawned");
    }
    
    void SetupNavMesh()
    {
        // Create NavMesh surface
        GameObject navMeshObj = new GameObject("NavMeshSurface");
        NavMeshSurface navMeshSurface = navMeshObj.AddComponent<NavMeshSurface>();
        
        // Configure NavMesh settings
        navMeshSurface.collectObjects = Unity.AI.Navigation.CollectObjects.All;
        navMeshSurface.useGeometry = NavMeshCollectGeometry.RenderMeshes;
        
        // Build NavMesh
        navMeshSurface.BuildNavMesh();
        
        spawnedObjects.Add(navMeshObj);
        
        Debug.Log("🗺️ NavMesh created and baked");
    }
    
    void SetupTrainingScenarios()
    {
        GameObject scenarioManager = new GameObject("PUBGTrainingScenarios");
        PUBGTrainingScenarios scenarios = scenarioManager.AddComponent<PUBGTrainingScenarios>();
        
        // Configure scenario system
        scenarios.enemyPrefab = enemyPrefab;
        scenarios.lootPrefab = lootPrefab;
        scenarios.weaponPrefab = weaponPrefab;
        scenarios.medkitPrefab = medkitPrefab;
        scenarios.coverPrefab = coverPrefab;
        
        spawnedObjects.Add(scenarioManager);
        
        Debug.Log("🎯 Training scenarios system enabled");
    }
    
    void SetupLighting()
    {
        // Ensure good lighting for training
        RenderSettings.ambientLight = Color.white * 0.3f;
        
        // Create directional light if none exists
        Light existingLight = FindObjectOfType<Light>();
        if (existingLight == null)
        {
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            lightObj.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
            
            spawnedObjects.Add(lightObj);
        }
    }
    
    void SetupCamera()
    {
        // Position main camera for good overview
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            mainCamera.transform.position = new Vector3(0f, 15f, -20f);
            mainCamera.transform.rotation = Quaternion.Euler(30f, 0f, 0f);
        }
    }
    
    Vector3 GetRandomSpawnPosition(float minDistance, float maxDistance)
    {
        float angle = Random.Range(0f, 360f);
        float distance = Random.Range(minDistance, maxDistance);
        
        return new Vector3(
            Mathf.Sin(angle * Mathf.Deg2Rad) * distance,
            0f,
            Mathf.Cos(angle * Mathf.Deg2Rad) * distance
        );
    }
    
    // Public methods for runtime control
    public void ResetArena()
    {
        SetupTrainingArena();
    }
    
    public void SpawnAdditionalEnemies(int count)
    {
        for (int i = 0; i < count; i++)
        {
            Vector3 spawnPos = GetRandomSpawnPosition(15f, 25f);
            
            GameObject enemy;
            if (enemyPrefab != null)
            {
                enemy = Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
            }
            else
            {
                enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                enemy.GetComponent<Renderer>().material.color = Color.red;
                enemy.AddComponent<EnemyAI>();
                enemy.AddComponent<HealthSystem>();
                enemy.AddComponent<NavMeshAgent>();
            }
            
            enemy.name = $"AdditionalEnemy_{i}";
            enemy.layer = 8;
            spawnedObjects.Add(enemy);
        }
        
        Debug.Log($"👹 {count} additional enemies spawned");
    }
    
    public void CreateDownedPlayerScenario()
    {
        // Simulate player being downed for revive training
        if (playerTransform != null)
        {
            HealthSystem playerHealth = playerTransform.GetComponent<HealthSystem>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(playerHealth.currentHealth);
            }
            
            PlayerController playerController = playerTransform.GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.GoDown();
            }
        }
        
        Debug.Log("🚑 Downed player scenario created");
    }
}
