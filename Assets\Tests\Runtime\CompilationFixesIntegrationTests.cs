using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Integration tests to verify that all compilation fixes work together correctly
    /// </summary>
    public class CompilationFixesIntegrationTests
    {
        private GameObject playerObject;
        private PlayerController playerController;
        private GameObject agentObject;
        private SquadMateAgent squadMateAgent;
        private GameObject scenarioObject;
        private PUBGTrainingScenarios trainingScenarios;
        private GameObject rewardObject;
        private SupportAIReward supportAIReward;

        [SetUp]
        public void SetUp()
        {
            // Create player
            playerObject = new GameObject("TestPlayer");
            playerController = playerObject.AddComponent<PlayerController>();
            playerObject.AddComponent<CharacterController>();
            playerController.Start();

            // Create agent
            agentObject = new GameObject("TestAgent");
            squadMateAgent = agentObject.AddComponent<SquadMateAgent>();
            agentObject.AddComponent<Rigidbody>();
            agentObject.AddComponent<CapsuleCollider>();

            // Create scenario manager
            scenarioObject = new GameObject("TestScenarios");
            trainingScenarios = scenarioObject.AddComponent<PUBGTrainingScenarios>();

            // Create reward system
            rewardObject = new GameObject("TestReward");
            supportAIReward = rewardObject.AddComponent<SupportAIReward>();
        }

        [TearDown]
        public void TearDown()
        {
            if (playerObject != null) Object.DestroyImmediate(playerObject);
            if (agentObject != null) Object.DestroyImmediate(agentObject);
            if (scenarioObject != null) Object.DestroyImmediate(scenarioObject);
            if (rewardObject != null) Object.DestroyImmediate(rewardObject);
        }

        [Test]
        public void Integration_PlayerController_IsDownedProperty_WorksWithSupportAIReward()
        {
            // Test that SupportAIReward can access PlayerController.isDowned
            Assert.IsFalse(playerController.isDowned, "Player should not be downed initially");
            
            // Down the player
            playerController.GoDown();
            Assert.IsTrue(playerController.isDowned, "Player should be downed");
            
            // SupportAIReward should be able to access this property
            // (This tests the fix for the compilation error in SupportAIReward.cs line 254)
            bool isDownedFromReward = playerController.isDowned;
            Assert.IsTrue(isDownedFromReward, "SupportAIReward should be able to access isDowned property");
        }

        [Test]
        public void Integration_SquadMateAgent_HasWeapon_WorksWithSupportAIMasterController()
        {
            // Test that SupportAIMasterController can access SquadMateAgent.hasWeapon
            Assert.IsFalse(squadMateAgent.hasWeapon, "Agent should not have weapon initially");
            
            // Set weapon state
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Agent should have weapon");
            
            // SupportAIMasterController should be able to access this property
            // (This tests the fix for the compilation error in SupportAIMasterController.cs line 115)
            bool hasWeaponFromController = squadMateAgent.hasWeapon;
            Assert.IsTrue(hasWeaponFromController, "SupportAIMasterController should be able to access hasWeapon");
        }

        [Test]
        public void Integration_PUBGTrainingScenarios_SwitchToNextScenario_WorksWithSupportAIMasterController()
        {
            // Test that SupportAIMasterController can call PUBGTrainingScenarios.SwitchToNextScenario
            // (This tests the fix for the compilation error in SupportAIMasterController.cs line 221)
            
            PUBGScenarioType initialScenario = trainingScenarios.currentScenario;
            
            // SupportAIMasterController should be able to call this method
            Assert.DoesNotThrow(() => trainingScenarios.SwitchToNextScenario(), 
                "SupportAIMasterController should be able to call SwitchToNextScenario");
        }

        [Test]
        public void Integration_AllFixedComponents_WorkTogether()
        {
            // Comprehensive test that all fixed components work together
            
            // 1. Test PlayerController.isDowned
            playerController.GoDown();
            Assert.IsTrue(playerController.isDowned, "PlayerController.isDowned should work");
            
            // 2. Test SquadMateAgent.hasWeapon
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "SquadMateAgent.hasWeapon should be accessible");
            
            // 3. Test PUBGTrainingScenarios.SwitchToNextScenario
            Assert.DoesNotThrow(() => trainingScenarios.SwitchToNextScenario(), 
                "PUBGTrainingScenarios.SwitchToNextScenario should be callable");
            
            // 4. Test that all components can interact
            bool playerDowned = playerController.isDowned;
            bool agentHasWeapon = squadMateAgent.hasWeapon;
            
            Assert.IsTrue(playerDowned && agentHasWeapon, 
                "All components should maintain their state correctly");
        }

        [Test]
        public void Integration_PUBGTrainingArenaSetup_DebugLogFix()
        {
            // Test that Debug.Log (not Debug.log) works correctly
            // This verifies the fix for PUBGTrainingArenaSetup.cs line 504
            
            Assert.DoesNotThrow(() => Debug.Log("Test message"), 
                "Debug.Log should work correctly");
            
            // Test that we can call Debug.Log in the context of our fixes
            Assert.DoesNotThrow(() => {
                if (playerController.isDowned)
                {
                    Debug.Log("Player is downed - this should work");
                }
            }, "Debug.Log should work in conditional contexts");
        }

        [Test]
        public void Integration_CrossComponent_Communication()
        {
            // Test that components can communicate using the fixed properties/methods
            
            // Scenario 1: Agent checks if player is downed
            playerController.GoDown();
            bool canAgentSeePlayerDowned = playerController.isDowned;
            Assert.IsTrue(canAgentSeePlayerDowned, "Agent should be able to check if player is downed");
            
            // Scenario 2: Controller checks if agent has weapon
            squadMateAgent.hasWeapon = true;
            bool canControllerSeeAgentWeapon = squadMateAgent.hasWeapon;
            Assert.IsTrue(canControllerSeeAgentWeapon, "Controller should be able to check if agent has weapon");
            
            // Scenario 3: Master controller can switch scenarios
            Assert.DoesNotThrow(() => trainingScenarios.SwitchToNextScenario(), 
                "Master controller should be able to switch scenarios");
        }

        [Test]
        public void Integration_ErrorConditions_HandledGracefully()
        {
            // Test that error conditions are handled gracefully with our fixes
            
            // Test null safety
            Assert.DoesNotThrow(() => {
                bool isDowned = playerController?.isDowned ?? false;
                bool hasWeapon = squadMateAgent?.hasWeapon ?? false;
            }, "Null-safe access should work");
            
            // Test rapid state changes
            Assert.DoesNotThrow(() => {
                for (int i = 0; i < 5; i++)
                {
                    playerController.GoDown();
                    playerController.Revive();
                    squadMateAgent.hasWeapon = !squadMateAgent.hasWeapon;
                    trainingScenarios.SwitchToNextScenario();
                }
            }, "Rapid state changes should not cause errors");
        }

        [Test]
        public void Integration_PerformanceImpact_Minimal()
        {
            // Test that our fixes don't have significant performance impact
            
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            
            // Perform many operations
            for (int i = 0; i < 1000; i++)
            {
                bool _ = playerController.isDowned;
                bool __ = squadMateAgent.hasWeapon;
                if (i % 100 == 0)
                {
                    trainingScenarios.SwitchToNextScenario();
                }
            }
            
            stopwatch.Stop();
            
            // Should complete quickly (less than 100ms for 1000 operations)
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 100, 
                "Property access should be performant");
        }
    }
}
