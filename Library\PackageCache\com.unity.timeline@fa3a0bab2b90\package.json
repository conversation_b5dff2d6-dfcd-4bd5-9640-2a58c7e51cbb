{"name": "com.unity.timeline", "displayName": "Timeline", "version": "1.8.8", "unity": "2021.3", "keywords": ["unity", "animation", "editor", "timeline", "tools"], "description": "Use Unity Timeline to create cinematic content, game-play sequences, audio sequences, and complex particle effects.", "dependencies": {"com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "relatedPackages": {"com.unity.timeline.tests": "1.8.8"}, "_upm": {"changelog": "### Fixed\n\n- Fixed bug where the first property, when it is a collection of objects, of a TrackAsset would not be properly displayed in the inspector.\n- TimelineAsset.EditorSettings.SetStandardFrameRate would incorrectly throw an ArgumentException if given a valid StandardFrameRates, this has been corrected.\n- Clip blends will now be computed when using the API to add clips to tracks - IN-66759\n- Improved performance when evaluating Timeline Playables with a large number of clip based Animation Tracks (TB-259)\n- Updated the Gameplay Sequence sample to show materials when using the Universal and HD Render Pipelines."}, "upmCi": {"footprint": "2fde19afc1f3a164420b2363c147b8d2ba71ffe0"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git", "type": "git", "revision": "b01f8fc6766a468476830dec57fdf694f16104e6"}, "samples": [{"displayName": "Customization Samples", "description": "This sample demonstrates how to create custom timeline tracks, clips, markers and actions.", "path": "Samples~/Customization"}, {"displayName": "Gameplay Sequence Demo", "description": "This sample demonstrates how Timeline can be used to create a small in-game moment, using built-in Timeline tracks.", "path": "Samples~/GameplaySequenceDemo"}], "_fingerprint": "fa3a0bab2b909389f8e6c20d5ff275c9e15ae0a2"}