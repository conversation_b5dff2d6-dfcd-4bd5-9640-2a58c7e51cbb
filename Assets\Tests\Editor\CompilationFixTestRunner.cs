using UnityEngine;
using UnityEditor;
using UnityEditor.TestTools.TestRunner.Api;
using System.Collections.Generic;

namespace SquadMateAI.Tests.Editor
{
    /// <summary>
    /// Editor script to run compilation fix tests and generate reports
    /// </summary>
    public class CompilationFixTestRunner : EditorWindow, ICallbacks
    {
        private TestRunnerApi testRunnerApi;
        private bool isRunningTests = false;
        private List<string> testResults = new List<string>();
        private Vector2 scrollPosition;

        [MenuItem("SquadMateAI/Run Compilation Fix Tests")]
        public static void ShowWindow()
        {
            GetWindow<CompilationFixTestRunner>("Compilation Fix Tests");
        }

        private void OnEnable()
        {
            testRunnerApi = ScriptableObject.CreateInstance<TestRunnerApi>();
            testRunnerApi.RegisterCallbacks(this);
        }

        private void OnDisable()
        {
            if (testRunnerApi != null)
            {
                testRunnerApi.UnregisterCallbacks(this);
            }
        }

        private void OnGUI()
        {
            GUILayout.Label("SquadMateAI Compilation Fix Tests", EditorStyles.boldLabel);
            GUILayout.Space(10);

            if (GUILayout.Button("Run All Tests", GUILayout.Height(30)))
            {
                RunAllTests();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Run Runtime Tests Only", GUILayout.Height(25)))
            {
                RunRuntimeTests();
            }

            if (GUILayout.Button("Run Integration Tests Only", GUILayout.Height(25)))
            {
                RunIntegrationTests();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Verify Compilation Fixes", GUILayout.Height(25)))
            {
                VerifyCompilationFixes();
            }

            GUILayout.Space(20);

            // Test Results
            GUILayout.Label("Test Results:", EditorStyles.boldLabel);
            
            scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            foreach (string result in testResults)
            {
                GUILayout.Label(result, EditorStyles.wordWrappedLabel);
            }
            
            GUILayout.EndScrollView();

            if (isRunningTests)
            {
                GUILayout.Label("Running tests...", EditorStyles.centeredGreyMiniLabel);
            }
        }

        private void RunAllTests()
        {
            isRunningTests = true;
            testResults.Clear();
            testResults.Add("Starting all tests...");

            var filter = new Filter()
            {
                testMode = TestMode.PlayMode,
                assemblyNames = new[] { "SquadMateAI.Runtime.Tests" }
            };

            testRunnerApi.Execute(new ExecutionSettings(filter));
        }

        private void RunRuntimeTests()
        {
            isRunningTests = true;
            testResults.Clear();
            testResults.Add("Starting runtime tests...");

            var filter = new Filter()
            {
                testMode = TestMode.PlayMode,
                testNames = new[] { 
                    "SquadMateAI.Tests.PlayerControllerTests",
                    "SquadMateAI.Tests.SquadMateAgentTests",
                    "SquadMateAI.Tests.PUBGTrainingScenariosTests"
                }
            };

            testRunnerApi.Execute(new ExecutionSettings(filter));
        }

        private void RunIntegrationTests()
        {
            isRunningTests = true;
            testResults.Clear();
            testResults.Add("Starting integration tests...");

            var filter = new Filter()
            {
                testMode = TestMode.PlayMode,
                testNames = new[] { "SquadMateAI.Tests.CompilationFixesIntegrationTests" }
            };

            testRunnerApi.Execute(new ExecutionSettings(filter));
        }

        private void VerifyCompilationFixes()
        {
            testResults.Clear();
            testResults.Add("=== COMPILATION FIXES VERIFICATION ===");
            testResults.Add("");

            // Check PlayerController.isDowned property
            try
            {
                var playerGO = new GameObject("TestPlayer");
                var playerController = playerGO.AddComponent<PlayerController>();
                playerGO.AddComponent<CharacterController>();
                playerController.Start();
                
                bool isDowned = playerController.isDowned;
                testResults.Add("✅ PlayerController.isDowned property - ACCESSIBLE");
                
                DestroyImmediate(playerGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ PlayerController.isDowned property - ERROR: {e.Message}");
            }

            // Check SquadMateAgent.hasWeapon property
            try
            {
                var agentGO = new GameObject("TestAgent");
                var agent = agentGO.AddComponent<SquadMateAgent>();
                agentGO.AddComponent<Rigidbody>();
                agentGO.AddComponent<CapsuleCollider>();
                
                bool hasWeapon = agent.hasWeapon;
                agent.hasWeapon = true;
                testResults.Add("✅ SquadMateAgent.hasWeapon property - ACCESSIBLE & WRITABLE");
                
                DestroyImmediate(agentGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ SquadMateAgent.hasWeapon property - ERROR: {e.Message}");
            }

            // Check PUBGTrainingScenarios.SwitchToNextScenario method
            try
            {
                var scenarioGO = new GameObject("TestScenarios");
                var scenarios = scenarioGO.AddComponent<PUBGTrainingScenarios>();
                
                scenarios.SwitchToNextScenario();
                testResults.Add("✅ PUBGTrainingScenarios.SwitchToNextScenario() - CALLABLE");
                
                DestroyImmediate(scenarioGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ PUBGTrainingScenarios.SwitchToNextScenario() - ERROR: {e.Message}");
            }

            // Check Debug.Log (not Debug.log)
            try
            {
                Debug.Log("Test Debug.Log call");
                testResults.Add("✅ Debug.Log capitalization - CORRECT");
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ Debug.Log capitalization - ERROR: {e.Message}");
            }

            testResults.Add("");
            testResults.Add("=== VERIFICATION COMPLETE ===");
        }

        // ICallbacks implementation
        public void OnTestRunStarted(ITestRunSettings testRunSettings)
        {
            testResults.Add($"Test run started: {testRunSettings.filter.testMode}");
        }

        public void OnTestStarted(ITestResult test)
        {
            // Optional: Add individual test start logging
        }

        public void OnTestFinished(ITestResult result)
        {
            string status = result.testStatus == TestStatus.Passed ? "✅ PASS" : "❌ FAIL";
            testResults.Add($"{status}: {result.test.Name}");
            
            if (result.testStatus == TestStatus.Failed)
            {
                testResults.Add($"   Error: {result.message}");
            }
        }

        public void OnTestRunFinished(ITestRunSettings testRunSettings)
        {
            isRunningTests = false;
            testResults.Add("");
            testResults.Add("=== TEST RUN COMPLETE ===");
            Repaint();
        }

        public void RunStarted(ITestRunSettings testRunSettings)
        {
            OnTestRunStarted(testRunSettings);
        }

        public void RunFinished(ITestRunSettings testRunSettings)
        {
            OnTestRunFinished(testRunSettings);
        }

        public void TestStarted(ITestResult test)
        {
            OnTestStarted(test);
        }

        public void TestFinished(ITestResult result)
        {
            OnTestFinished(result);
        }
    }
}
