using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace SquadMateAI.Tests.Editor
{
    /// <summary>
    /// Editor script to verify compilation fixes
    /// </summary>
    public class CompilationFixTestRunner : EditorWindow
    {
        private List<string> testResults = new List<string>();
        private Vector2 scrollPosition;

        [MenuItem("SquadMateAI/Verify Compilation Fixes")]
        public static void ShowWindow()
        {
            GetWindow<CompilationFixTestRunner>("Compilation Fix Verification");
        }

        private void OnGUI()
        {
            GUILayout.Label("SquadMateAI Compilation Fix Verification", EditorStyles.boldLabel);
            GUILayout.Space(10);

            if (GUILayout.Button("Verify All Compilation Fixes", GUILayout.Height(30)))
            {
                VerifyCompilationFixes();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Open Unity Test Runner", GUILayout.Height(25)))
            {
                EditorApplication.ExecuteMenuItem("Window/General/Test Runner");
            }

            GUILayout.Space(20);

            // Test Results
            GUILayout.Label("Verification Results:", EditorStyles.boldLabel);

            scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));

            foreach (string result in testResults)
            {
                GUILayout.Label(result, EditorStyles.wordWrappedLabel);
            }

            GUILayout.EndScrollView();
        }



        private void VerifyCompilationFixes()
        {
            testResults.Clear();
            testResults.Add("=== COMPILATION FIXES VERIFICATION ===");
            testResults.Add("");

            // Check PlayerController.isDowned property
            try
            {
                var playerGO = new GameObject("TestPlayer");
                var playerController = playerGO.AddComponent<PlayerController>();
                playerGO.AddComponent<CharacterController>();
                playerController.Start();
                
                bool isDowned = playerController.isDowned;
                testResults.Add("✅ PlayerController.isDowned property - ACCESSIBLE");
                
                DestroyImmediate(playerGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ PlayerController.isDowned property - ERROR: {e.Message}");
            }

            // Check SquadMateAgent.hasWeapon property
            try
            {
                var agentGO = new GameObject("TestAgent");
                var agent = agentGO.AddComponent<SquadMateAgent>();
                agentGO.AddComponent<Rigidbody>();
                agentGO.AddComponent<CapsuleCollider>();
                
                bool hasWeapon = agent.hasWeapon;
                agent.hasWeapon = true;
                testResults.Add("✅ SquadMateAgent.hasWeapon property - ACCESSIBLE & WRITABLE");
                
                DestroyImmediate(agentGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ SquadMateAgent.hasWeapon property - ERROR: {e.Message}");
            }

            // Check PUBGTrainingScenarios.SwitchToNextScenario method
            try
            {
                var scenarioGO = new GameObject("TestScenarios");
                var scenarios = scenarioGO.AddComponent<PUBGTrainingScenarios>();
                
                scenarios.SwitchToNextScenario();
                testResults.Add("✅ PUBGTrainingScenarios.SwitchToNextScenario() - CALLABLE");
                
                DestroyImmediate(scenarioGO);
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ PUBGTrainingScenarios.SwitchToNextScenario() - ERROR: {e.Message}");
            }

            // Check Debug.Log (not Debug.log)
            try
            {
                Debug.Log("Test Debug.Log call");
                testResults.Add("✅ Debug.Log capitalization - CORRECT");
            }
            catch (System.Exception e)
            {
                testResults.Add($"❌ Debug.Log capitalization - ERROR: {e.Message}");
            }

            testResults.Add("");
            testResults.Add("=== VERIFICATION COMPLETE ===");
        }


    }
}
