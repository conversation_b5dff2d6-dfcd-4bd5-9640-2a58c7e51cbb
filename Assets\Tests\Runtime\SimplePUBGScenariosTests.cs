using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Simple test class for PUBGTrainingScenarios compilation fixes
    /// </summary>
    public class SimplePUBGScenariosTests : MonoBehaviour
    {
        [ContextMenu("Test PUBGTrainingScenarios.SwitchToNextScenario Method")]
        public void TestPUBGScenariosMethod()
        {
            Debug.Log("🧪 Testing PUBGTrainingScenarios.SwitchToNextScenario() method...");
            
            try
            {
                // Create test scenarios
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                // Test 1: Initial scenario
                PUBGScenarioType initialScenario = scenarios.currentScenario;
                Debug.Log($"  Initial scenario: {initialScenario}");
                
                // Test 2: Switch scenario
                scenarios.SwitchToNextScenario();
                PUBGScenarioType newScenario = scenarios.currentScenario;
                Debug.Log($"  After switch: {newScenario}");
                
                // Test 3: Multiple switches
                scenarios.SwitchToNextScenario();
                scenarios.SwitchToNextScenario();
                PUBGScenarioType finalScenario = scenarios.currentScenario;
                Debug.Log($"  After multiple switches: {finalScenario}");
                
                // Test 4: Scenario description
                string description = scenarios.GetCurrentScenarioDescription();
                Debug.Log($"  Current scenario description: {description}");
                
                // Clean up
                DestroyImmediate(testScenarios);
                
                // If we got here without exceptions, the method is callable
                bool testPassed = true;
                
                if (testPassed)
                {
                    Debug.Log("✅ PUBGTrainingScenarios.SwitchToNextScenario() method test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ PUBGTrainingScenarios.SwitchToNextScenario() method test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ PUBGTrainingScenarios.SwitchToNextScenario() method test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test PUBGScenarios External Access")]
        public void TestPUBGScenariosExternalAccess()
        {
            Debug.Log("🧪 Testing PUBGTrainingScenarios external access...");
            
            try
            {
                // Create test scenarios
                GameObject testScenarios = new GameObject("TestScenarios");
                PUBGTrainingScenarios scenarios = testScenarios.AddComponent<PUBGTrainingScenarios>();
                
                // Simulate external script access (like SupportAIMasterController)
                // This is what SupportAIMasterController.SwitchScenario() does:
                scenarios.SwitchToNextScenario();
                Debug.Log("  External script successfully called SwitchToNextScenario()");
                
                // Test rapid calls (like in demo mode)
                for (int i = 0; i < 5; i++)
                {
                    scenarios.SwitchToNextScenario();
                }
                Debug.Log("  Rapid scenario switching works");
                
                // Clean up
                DestroyImmediate(testScenarios);
                
                bool testPassed = true; // If we got here, external access works
                
                if (testPassed)
                {
                    Debug.Log("✅ PUBGTrainingScenarios external access test PASSED");
                }
                else
                {
                    Debug.LogWarning("❌ PUBGTrainingScenarios external access test FAILED");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ PUBGTrainingScenarios external access test ERROR: {e.Message}");
            }
        }
        
        [ContextMenu("Test All PUBGScenarios Features")]
        public void TestAllPUBGScenariosFeatures()
        {
            TestPUBGScenariosMethod();
            TestPUBGScenariosExternalAccess();
        }
    }
}
