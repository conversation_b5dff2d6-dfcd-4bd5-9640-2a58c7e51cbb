using UnityEngine;

namespace SquadMateAI.Tests
{
    /// <summary>
    /// Quick verification script that can be attached to a GameObject to test all compilation fixes
    /// </summary>
    public class QuickCompilationFixVerification : MonoBehaviour
    {
        [Header("Test Results")]
        public bool playerControllerIsDownedWorks = false;
        public bool squadMateAgentHasWeaponWorks = false;
        public bool pubgScenariosMethodWorks = false;
        public bool debugLogWorks = false;
        public bool allTestsPassed = false;

        [Header("Test Components")]
        public PlayerController testPlayer;
        public SquadMateAgent testAgent;
        public PUBGTrainingScenarios testScenarios;

        private void Start()
        {
            Debug.Log("🧪 Starting Quick Compilation Fix Verification...");
            RunAllVerificationTests();
        }

        [ContextMenu("Run Verification Tests")]
        public void RunAllVerificationTests()
        {
            Debug.Log("=== COMPILATION FIXES VERIFICATION ===");
            
            TestPlayerControllerIsDownedProperty();
            TestSquadMateAgentHasWeaponProperty();
            TestPUBGScenariosMethod();
            TestDebugLogCapitalization();
            
            allTestsPassed = playerControllerIsDownedWorks && 
                           squadMateAgentHasWeaponWorks && 
                           pubgScenariosMethodWorks && 
                           debugLogWorks;
            
            Debug.Log($"=== VERIFICATION COMPLETE - All Tests Passed: {allTestsPassed} ===");
            
            if (allTestsPassed)
            {
                Debug.Log("🎉 ALL COMPILATION FIXES WORKING CORRECTLY! 🎉");
            }
            else
            {
                Debug.LogWarning("⚠️ Some compilation fixes may have issues. Check individual test results.");
            }
        }

        private void TestPlayerControllerIsDownedProperty()
        {
            try
            {
                // Create or use existing player
                PlayerController player = testPlayer;
                if (player == null)
                {
                    GameObject playerGO = new GameObject("TestPlayer");
                    player = playerGO.AddComponent<PlayerController>();
                    playerGO.AddComponent<CharacterController>();
                    player.Start();
                }

                // Test the isDowned property
                bool initialState = player.isDowned;
                player.GoDown();
                bool downedState = player.isDowned;
                player.Revive();
                bool revivedState = player.isDowned;

                playerControllerIsDownedWorks = !initialState && downedState && !revivedState;
                
                string result = playerControllerIsDownedWorks ? "✅ PASS" : "❌ FAIL";
                Debug.Log($"{result}: PlayerController.isDowned property test");

                // Clean up if we created a temporary object
                if (testPlayer == null && player != null)
                {
                    DestroyImmediate(player.gameObject);
                }
            }
            catch (System.Exception e)
            {
                playerControllerIsDownedWorks = false;
                Debug.LogError($"❌ FAIL: PlayerController.isDowned property test - {e.Message}");
            }
        }

        private void TestSquadMateAgentHasWeaponProperty()
        {
            try
            {
                // Create or use existing agent
                SquadMateAgent agent = testAgent;
                if (agent == null)
                {
                    GameObject agentGO = new GameObject("TestAgent");
                    agent = agentGO.AddComponent<SquadMateAgent>();
                    agentGO.AddComponent<Rigidbody>();
                    agentGO.AddComponent<CapsuleCollider>();
                }

                // Test the hasWeapon property
                bool initialState = agent.hasWeapon;
                agent.hasWeapon = true;
                bool weaponState = agent.hasWeapon;
                agent.hasWeapon = false;
                bool noWeaponState = agent.hasWeapon;

                squadMateAgentHasWeaponWorks = !initialState && weaponState && !noWeaponState;
                
                string result = squadMateAgentHasWeaponWorks ? "✅ PASS" : "❌ FAIL";
                Debug.Log($"{result}: SquadMateAgent.hasWeapon property test");

                // Clean up if we created a temporary object
                if (testAgent == null && agent != null)
                {
                    DestroyImmediate(agent.gameObject);
                }
            }
            catch (System.Exception e)
            {
                squadMateAgentHasWeaponWorks = false;
                Debug.LogError($"❌ FAIL: SquadMateAgent.hasWeapon property test - {e.Message}");
            }
        }

        private void TestPUBGScenariosMethod()
        {
            try
            {
                // Create or use existing scenarios
                PUBGTrainingScenarios scenarios = testScenarios;
                if (scenarios == null)
                {
                    GameObject scenarioGO = new GameObject("TestScenarios");
                    scenarios = scenarioGO.AddComponent<PUBGTrainingScenarios>();
                }

                // Test the SwitchToNextScenario method
                PUBGScenarioType initialScenario = scenarios.currentScenario;
                scenarios.SwitchToNextScenario();
                // Method should execute without error

                pubgScenariosMethodWorks = true;
                
                string result = pubgScenariosMethodWorks ? "✅ PASS" : "❌ FAIL";
                Debug.Log($"{result}: PUBGTrainingScenarios.SwitchToNextScenario() method test");

                // Clean up if we created a temporary object
                if (testScenarios == null && scenarios != null)
                {
                    DestroyImmediate(scenarios.gameObject);
                }
            }
            catch (System.Exception e)
            {
                pubgScenariosMethodWorks = false;
                Debug.LogError($"❌ FAIL: PUBGTrainingScenarios.SwitchToNextScenario() method test - {e.Message}");
            }
        }

        private void TestDebugLogCapitalization()
        {
            try
            {
                // Test that Debug.Log (not Debug.log) works
                Debug.Log("Debug.Log capitalization test - this message confirms it works");
                debugLogWorks = true;
                
                string result = debugLogWorks ? "✅ PASS" : "❌ FAIL";
                Debug.Log($"{result}: Debug.Log capitalization test");
            }
            catch (System.Exception e)
            {
                debugLogWorks = false;
                Debug.LogError($"❌ FAIL: Debug.Log capitalization test - {e.Message}");
            }
        }

        // Public methods for external testing
        public bool VerifyPlayerControllerFix()
        {
            TestPlayerControllerIsDownedProperty();
            return playerControllerIsDownedWorks;
        }

        public bool VerifySquadMateAgentFix()
        {
            TestSquadMateAgentHasWeaponProperty();
            return squadMateAgentHasWeaponWorks;
        }

        public bool VerifyPUBGScenariosFix()
        {
            TestPUBGScenariosMethod();
            return pubgScenariosMethodWorks;
        }

        public bool VerifyDebugLogFix()
        {
            TestDebugLogCapitalization();
            return debugLogWorks;
        }

        // Inspector button for easy testing
        [ContextMenu("Test Individual - PlayerController")]
        private void TestPlayerControllerOnly()
        {
            TestPlayerControllerIsDownedProperty();
        }

        [ContextMenu("Test Individual - SquadMateAgent")]
        private void TestSquadMateAgentOnly()
        {
            TestSquadMateAgentHasWeaponProperty();
        }

        [ContextMenu("Test Individual - PUBGScenarios")]
        private void TestPUBGScenariosOnly()
        {
            TestPUBGScenariosMethod();
        }

        [ContextMenu("Test Individual - Debug.Log")]
        private void TestDebugLogOnly()
        {
            TestDebugLogCapitalization();
        }
    }
}
