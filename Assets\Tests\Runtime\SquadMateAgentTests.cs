using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Unity.MLAgents;

namespace SquadMateAI.Tests
{
    public class SquadMateAgentTests
    {
        private GameObject agentObject;
        private SquadMateAgent squadMateAgent;
        private GameObject playerObject;
        private PlayerController playerController;

        [SetUp]
        public void SetUp()
        {
            // Create test agent object
            agentObject = new GameObject("TestSquadMateAgent");
            squadMateAgent = agentObject.AddComponent<SquadMateAgent>();
            
            // Add required components for SquadMateAgent
            agentObject.AddComponent<Rigidbody>();
            agentObject.AddComponent<CapsuleCollider>();
            
            // Create test player object
            playerObject = new GameObject("TestPlayer");
            playerController = playerObject.AddComponent<PlayerController>();
            playerObject.AddComponent<CharacterController>();
            
            // Initialize player
            playerController.Start();
        }

        [TearDown]
        public void TearDown()
        {
            if (agentObject != null)
            {
                Object.DestroyImmediate(agentObject);
            }
            if (playerObject != null)
            {
                Object.DestroyImmediate(playerObject);
            }
        }

        [Test]
        public void SquadMateAgent_HasWeaponProperty_IsPublic()
        {
            // Test that hasWeapon property is accessible (public)
            Assert.IsNotNull(squadMateAgent);
            
            // Should be able to read the property
            bool initialWeaponState = squadMateAgent.hasWeapon;
            Assert.IsFalse(initialWeaponState, "Agent should not have weapon initially");
            
            // Should be able to write to the property
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Should be able to set hasWeapon to true");
            
            squadMateAgent.hasWeapon = false;
            Assert.IsFalse(squadMateAgent.hasWeapon, "Should be able to set hasWeapon to false");
        }

        [Test]
        public void SquadMateAgent_HasWeaponProperty_DefaultValue()
        {
            // Test that hasWeapon starts as false
            Assert.IsFalse(squadMateAgent.hasWeapon, "hasWeapon should default to false");
        }

        [Test]
        public void SquadMateAgent_HasWeaponProperty_CanBeModified()
        {
            // Test that external scripts can modify hasWeapon
            Assert.IsFalse(squadMateAgent.hasWeapon, "Initially should not have weapon");
            
            // Simulate weapon pickup
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Should have weapon after pickup");
            
            // Simulate weapon loss
            squadMateAgent.hasWeapon = false;
            Assert.IsFalse(squadMateAgent.hasWeapon, "Should not have weapon after loss");
        }

        [Test]
        public void SquadMateAgent_HasWeaponProperty_PersistsThroughFrames()
        {
            // Test that hasWeapon value persists
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Should have weapon");
            
            // Simulate frame update (if Update method exists and is safe to call)
            // Note: We can't easily test Update() in unit tests, but we can test persistence
            bool weaponState = squadMateAgent.hasWeapon;
            Assert.IsTrue(weaponState, "Weapon state should persist");
        }

        [Test]
        public void SquadMateAgent_WeaponPickup_Integration()
        {
            // Test weapon pickup integration (if PickupLoot method is accessible)
            Assert.IsFalse(squadMateAgent.hasWeapon, "Should not have weapon initially");
            
            // Create a weapon loot item
            GameObject lootObject = new GameObject("WeaponLoot");
            LootItem lootItem = lootObject.AddComponent<LootItem>();
            lootItem.lootType = LootItem.LootType.Weapon;
            
            // Test that we can manually set weapon state (simulating pickup)
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Should have weapon after simulated pickup");
            
            Object.DestroyImmediate(lootObject);
        }

        [Test]
        public void SquadMateAgent_WeaponState_ResetOnAgentReset()
        {
            // Test that weapon state resets properly
            squadMateAgent.hasWeapon = true;
            Assert.IsTrue(squadMateAgent.hasWeapon, "Should have weapon");
            
            // Manually reset weapon state (simulating agent reset)
            squadMateAgent.hasWeapon = false;
            Assert.IsFalse(squadMateAgent.hasWeapon, "Should not have weapon after reset");
        }

        [Test]
        public void SquadMateAgent_WeaponState_AccessibleFromOtherScripts()
        {
            // Test that other scripts can access hasWeapon (like SupportAIMasterController)
            
            // Simulate SupportAIMasterController accessing hasWeapon
            bool weaponStatus = squadMateAgent.hasWeapon;
            Assert.IsFalse(weaponStatus, "Other scripts should be able to read hasWeapon");
            
            // Simulate setting weapon state from another script
            squadMateAgent.hasWeapon = true;
            weaponStatus = squadMateAgent.hasWeapon;
            Assert.IsTrue(weaponStatus, "Other scripts should be able to read updated hasWeapon");
        }

        [Test]
        public void SquadMateAgent_WeaponState_BooleanOperations()
        {
            // Test boolean operations with hasWeapon
            Assert.IsFalse(squadMateAgent.hasWeapon, "Initial state should be false");
            
            // Test logical operations
            bool canAttack = squadMateAgent.hasWeapon && true;
            Assert.IsFalse(canAttack, "Should not be able to attack without weapon");
            
            squadMateAgent.hasWeapon = true;
            canAttack = squadMateAgent.hasWeapon && true;
            Assert.IsTrue(canAttack, "Should be able to attack with weapon");
            
            // Test ternary operations
            string status = squadMateAgent.hasWeapon ? "Armed" : "Unarmed";
            Assert.AreEqual("Armed", status, "Status should reflect weapon state");
        }
    }
}
