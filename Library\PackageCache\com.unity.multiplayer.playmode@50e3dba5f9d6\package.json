{"name": "com.unity.multiplayer.playmode", "displayName": "Multiplayer Play Mode", "version": "1.4.3", "unity": "6000.1", "unityRelease": "0b1", "description": "Multiplayer Play Mode is a feature that enables multiple editor instances to be opened simultaneously on the same development device.", "dependencies": {"com.unity.nuget.newtonsoft-json": "2.0.2"}, "relatedPackages": {"com.unity.multiplayer.workflows.tests.common": "1.4.3", "com.unity.multiplayer.playmode.tests": "1.4.3"}, "_upm": {"changelog": "### Fixed \n- Using non authorized characters when creating a new Scenario configuration is now returning a warning rather than an error. \n- Fixed an issue where toggling Simulator mode in the Virtual Player's Game Window fails to show\n- Removed the minimum height restriction on PlayModePopupContentWindow to prevent excessive height when only one or two scenario configs are present\n- Fixed low-resolution icons in various windows\n- Set a minimum width for the scenario config list in the Scenario Config window to prevent resizing it below a usable size\n- Added an info HelpBox that appears when no scenario is selected, preventing the window from appearing blank\n- Updated the Multiplayer role dropdown to display “Client And Server” with proper spacing in the Scenario Config window\n- Fixed an issue where icons did not adapt when switching between dark and light mode in the editor in PlayModePopupContentWindow and PlayModeStatusWindow"}, "upmCi": {"footprint": "3017fb52c5980d296ff48b51d8a8285505f9b1ae"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.multiplayer.playmode@1.4/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/multiplayer-workflows.git", "type": "git", "revision": "b9f0032e9cb6af582a1cc5fd03547d45db6ee7a3"}, "_fingerprint": "50e3dba5f9d669a30d286824f8656452f51a6790"}